using LicenseManager.Models;
using LicenseManager.Data.Repositories;

namespace LicenseManager.Services;

/// <summary>
/// 数据服务接口
/// 提供统一的数据访问和管理功能
/// </summary>
public interface IDataService
{
    /// <summary>
    /// 许可证仓储
    /// </summary>
    ILicenseRepository Licenses { get; }

    /// <summary>
    /// 授权类型仓储
    /// </summary>
    IRepository<AuthorizationType> AuthorizationTypes { get; }

    /// <summary>
    /// 应用程序模板仓储
    /// </summary>
    IRepository<AppTemplate> AppTemplates { get; }

    /// <summary>
    /// 许可证信息仓储
    /// </summary>
    IRepository<LicenseInfo> LicenseInfos { get; }

    /// <summary>
    /// 硬件指纹仓储
    /// </summary>
    IRepository<HardwareFingerprint> HardwareFingerprints { get; }

    /// <summary>
    /// 初始化数据库
    /// </summary>
    /// <param name="recreateDatabase">是否重新创建数据库</param>
    /// <returns>是否成功</returns>
    Task<bool> InitializeDatabaseAsync(bool recreateDatabase = false);

    /// <summary>
    /// 检查数据库连接
    /// </summary>
    /// <returns>连接状态</returns>
    Task<DatabaseConnectionStatus> CheckDatabaseConnectionAsync();

    /// <summary>
    /// 备份数据库
    /// </summary>
    /// <param name="backupPath">备份路径</param>
    /// <returns>备份结果</returns>
    Task<DatabaseBackupResult> BackupDatabaseAsync(string backupPath);

    /// <summary>
    /// 恢复数据库
    /// </summary>
    /// <param name="backupPath">备份文件路径</param>
    /// <returns>恢复结果</returns>
    Task<DatabaseRestoreResult> RestoreDatabaseAsync(string backupPath);

    /// <summary>
    /// 获取数据库统计信息
    /// </summary>
    /// <returns>统计信息</returns>
    Task<DatabaseStatistics> GetDatabaseStatisticsAsync();

    /// <summary>
    /// 清理过期数据
    /// </summary>
    /// <param name="options">清理选项</param>
    /// <returns>清理结果</returns>
    Task<DataCleanupResult> CleanupExpiredDataAsync(DataCleanupOptions options);

    /// <summary>
    /// 优化数据库
    /// </summary>
    /// <returns>优化结果</returns>
    Task<DatabaseOptimizationResult> OptimizeDatabaseAsync();

    /// <summary>
    /// 导出数据
    /// </summary>
    /// <param name="exportOptions">导出选项</param>
    /// <returns>导出结果</returns>
    Task<DataExportResult> ExportDataAsync(DataExportOptions exportOptions);

    /// <summary>
    /// 导入数据
    /// </summary>
    /// <param name="importOptions">导入选项</param>
    /// <returns>导入结果</returns>
    Task<DataImportResult> ImportDataAsync(DataImportOptions importOptions);

    /// <summary>
    /// 执行数据迁移
    /// </summary>
    /// <param name="targetVersion">目标版本</param>
    /// <returns>迁移结果</returns>
    Task<DataMigrationResult> MigrateDataAsync(string targetVersion);

    /// <summary>
    /// 验证数据完整性
    /// </summary>
    /// <returns>验证结果</returns>
    Task<DataIntegrityResult> ValidateDataIntegrityAsync();

    /// <summary>
    /// 获取数据库架构信息
    /// </summary>
    /// <returns>架构信息</returns>
    Task<DatabaseSchemaInfo> GetDatabaseSchemaAsync();

    /// <summary>
    /// 执行原始SQL查询
    /// </summary>
    /// <param name="sql">SQL语句</param>
    /// <param name="parameters">参数</param>
    /// <returns>查询结果</returns>
    Task<QueryResult> ExecuteRawSqlAsync(string sql, params object[] parameters);

    /// <summary>
    /// 开始事务
    /// </summary>
    /// <returns>事务对象</returns>
    Task<IRepositoryTransaction> BeginTransactionAsync();

    /// <summary>
    /// 保存所有更改
    /// </summary>
    /// <returns>受影响的行数</returns>
    Task<int> SaveChangesAsync();
}

/// <summary>
/// 数据库连接状态
/// </summary>
public class DatabaseConnectionStatus
{
    /// <summary>
    /// 是否连接成功
    /// </summary>
    public bool IsConnected { get; set; }

    /// <summary>
    /// 连接字符串
    /// </summary>
    public string ConnectionString { get; set; } = string.Empty;

    /// <summary>
    /// 数据库版本
    /// </summary>
    public string? DatabaseVersion { get; set; }

    /// <summary>
    /// 连接时间（毫秒）
    /// </summary>
    public long ConnectionTimeMs { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 检查时间
    /// </summary>
    public DateTime CheckedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 数据库备份结果
/// </summary>
public class DatabaseBackupResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 备份文件路径
    /// </summary>
    public string? BackupFilePath { get; set; }

    /// <summary>
    /// 备份文件大小（字节）
    /// </summary>
    public long BackupFileSize { get; set; }

    /// <summary>
    /// 备份耗时（毫秒）
    /// </summary>
    public long ElapsedMilliseconds { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 备份时间
    /// </summary>
    public DateTime BackupTime { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 数据库恢复结果
/// </summary>
public class DatabaseRestoreResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 恢复的记录数
    /// </summary>
    public int RestoredRecords { get; set; }

    /// <summary>
    /// 恢复耗时（毫秒）
    /// </summary>
    public long ElapsedMilliseconds { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 警告消息
    /// </summary>
    public string[]? Warnings { get; set; }

    /// <summary>
    /// 恢复时间
    /// </summary>
    public DateTime RestoreTime { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 数据库统计信息
/// </summary>
public class DatabaseStatistics
{
    /// <summary>
    /// 数据库大小（字节）
    /// </summary>
    public long DatabaseSize { get; set; }

    /// <summary>
    /// 表统计信息
    /// </summary>
    public Dictionary<string, TableStatistics> TableStatistics { get; set; } = new();

    /// <summary>
    /// 索引统计信息
    /// </summary>
    public Dictionary<string, IndexStatistics> IndexStatistics { get; set; } = new();

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 表统计信息
/// </summary>
public class TableStatistics
{
    /// <summary>
    /// 表名
    /// </summary>
    public string TableName { get; set; } = string.Empty;

    /// <summary>
    /// 记录数
    /// </summary>
    public long RecordCount { get; set; }

    /// <summary>
    /// 表大小（字节）
    /// </summary>
    public long TableSize { get; set; }

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime? LastUpdated { get; set; }
}

/// <summary>
/// 索引统计信息
/// </summary>
public class IndexStatistics
{
    /// <summary>
    /// 索引名
    /// </summary>
    public string IndexName { get; set; } = string.Empty;

    /// <summary>
    /// 表名
    /// </summary>
    public string TableName { get; set; } = string.Empty;

    /// <summary>
    /// 索引大小（字节）
    /// </summary>
    public long IndexSize { get; set; }

    /// <summary>
    /// 使用次数
    /// </summary>
    public long UsageCount { get; set; }
}

/// <summary>
/// 数据清理选项
/// </summary>
public class DataCleanupOptions
{
    /// <summary>
    /// 清理过期许可证
    /// </summary>
    public bool CleanupExpiredLicenses { get; set; } = true;

    /// <summary>
    /// 过期天数阈值
    /// </summary>
    public int ExpiredDaysThreshold { get; set; } = 90;

    /// <summary>
    /// 清理日志记录
    /// </summary>
    public bool CleanupLogs { get; set; } = true;

    /// <summary>
    /// 日志保留天数
    /// </summary>
    public int LogRetentionDays { get; set; } = 30;

    /// <summary>
    /// 清理临时文件
    /// </summary>
    public bool CleanupTempFiles { get; set; } = true;

    /// <summary>
    /// 压缩数据库
    /// </summary>
    public bool CompactDatabase { get; set; } = false;
}

/// <summary>
/// 数据清理结果
/// </summary>
public class DataCleanupResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 清理的记录数
    /// </summary>
    public int CleanedRecords { get; set; }

    /// <summary>
    /// 释放的空间（字节）
    /// </summary>
    public long FreedSpace { get; set; }

    /// <summary>
    /// 清理耗时（毫秒）
    /// </summary>
    public long ElapsedMilliseconds { get; set; }

    /// <summary>
    /// 清理详情
    /// </summary>
    public Dictionary<string, int> CleanupDetails { get; set; } = new();

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// 数据库优化结果
/// </summary>
public class DatabaseOptimizationResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 优化前大小（字节）
    /// </summary>
    public long SizeBefore { get; set; }

    /// <summary>
    /// 优化后大小（字节）
    /// </summary>
    public long SizeAfter { get; set; }

    /// <summary>
    /// 节省的空间（字节）
    /// </summary>
    public long SpaceSaved => SizeBefore - SizeAfter;

    /// <summary>
    /// 优化耗时（毫秒）
    /// </summary>
    public long ElapsedMilliseconds { get; set; }

    /// <summary>
    /// 优化操作列表
    /// </summary>
    public string[] OptimizationOperations { get; set; } = Array.Empty<string>();

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// 数据导出选项
/// </summary>
public class DataExportOptions
{
    /// <summary>
    /// 导出格式
    /// </summary>
    public DataExportFormat Format { get; set; } = DataExportFormat.Json;

    /// <summary>
    /// 导出路径
    /// </summary>
    public string ExportPath { get; set; } = string.Empty;

    /// <summary>
    /// 要导出的表
    /// </summary>
    public string[]? Tables { get; set; }

    /// <summary>
    /// 是否包含架构
    /// </summary>
    public bool IncludeSchema { get; set; } = true;

    /// <summary>
    /// 是否包含数据
    /// </summary>
    public bool IncludeData { get; set; } = true;

    /// <summary>
    /// 是否压缩
    /// </summary>
    public bool Compress { get; set; } = false;
}

/// <summary>
/// 数据导出结果
/// </summary>
public class DataExportResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 导出文件路径
    /// </summary>
    public string? ExportFilePath { get; set; }

    /// <summary>
    /// 导出的记录数
    /// </summary>
    public int ExportedRecords { get; set; }

    /// <summary>
    /// 文件大小（字节）
    /// </summary>
    public long FileSize { get; set; }

    /// <summary>
    /// 导出耗时（毫秒）
    /// </summary>
    public long ElapsedMilliseconds { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// 数据导入选项
/// </summary>
public class DataImportOptions
{
    /// <summary>
    /// 导入文件路径
    /// </summary>
    public string ImportFilePath { get; set; } = string.Empty;

    /// <summary>
    /// 导入格式
    /// </summary>
    public DataExportFormat Format { get; set; } = DataExportFormat.Json;

    /// <summary>
    /// 是否覆盖现有数据
    /// </summary>
    public bool OverwriteExisting { get; set; } = false;

    /// <summary>
    /// 是否验证数据
    /// </summary>
    public bool ValidateData { get; set; } = true;

    /// <summary>
    /// 批量大小
    /// </summary>
    public int BatchSize { get; set; } = 1000;
}

/// <summary>
/// 数据导入结果
/// </summary>
public class DataImportResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 导入的记录数
    /// </summary>
    public int ImportedRecords { get; set; }

    /// <summary>
    /// 跳过的记录数
    /// </summary>
    public int SkippedRecords { get; set; }

    /// <summary>
    /// 错误记录数
    /// </summary>
    public int ErrorRecords { get; set; }

    /// <summary>
    /// 导入耗时（毫秒）
    /// </summary>
    public long ElapsedMilliseconds { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 警告消息
    /// </summary>
    public string[]? Warnings { get; set; }
}

/// <summary>
/// 数据迁移结果
/// </summary>
public class DataMigrationResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 源版本
    /// </summary>
    public string? SourceVersion { get; set; }

    /// <summary>
    /// 目标版本
    /// </summary>
    public string? TargetVersion { get; set; }

    /// <summary>
    /// 迁移的记录数
    /// </summary>
    public int MigratedRecords { get; set; }

    /// <summary>
    /// 迁移耗时（毫秒）
    /// </summary>
    public long ElapsedMilliseconds { get; set; }

    /// <summary>
    /// 迁移步骤
    /// </summary>
    public string[] MigrationSteps { get; set; } = Array.Empty<string>();

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// 数据完整性结果
/// </summary>
public class DataIntegrityResult
{
    /// <summary>
    /// 是否通过验证
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 验证的表数量
    /// </summary>
    public int ValidatedTables { get; set; }

    /// <summary>
    /// 发现的问题数量
    /// </summary>
    public int IssuesFound { get; set; }

    /// <summary>
    /// 验证详情
    /// </summary>
    public Dictionary<string, ValidationIssue[]> ValidationDetails { get; set; } = new();

    /// <summary>
    /// 验证耗时（毫秒）
    /// </summary>
    public long ElapsedMilliseconds { get; set; }
}

/// <summary>
/// 验证问题
/// </summary>
public class ValidationIssue
{
    /// <summary>
    /// 问题类型
    /// </summary>
    public string IssueType { get; set; } = string.Empty;

    /// <summary>
    /// 问题描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 严重程度
    /// </summary>
    public IssueSeverity Severity { get; set; }

    /// <summary>
    /// 建议修复方案
    /// </summary>
    public string? SuggestedFix { get; set; }
}

/// <summary>
/// 数据库架构信息
/// </summary>
public class DatabaseSchemaInfo
{
    /// <summary>
    /// 数据库名称
    /// </summary>
    public string DatabaseName { get; set; } = string.Empty;

    /// <summary>
    /// 架构版本
    /// </summary>
    public string SchemaVersion { get; set; } = string.Empty;

    /// <summary>
    /// 表信息
    /// </summary>
    public Dictionary<string, TableInfo> Tables { get; set; } = new();

    /// <summary>
    /// 索引信息
    /// </summary>
    public Dictionary<string, IndexInfo> Indexes { get; set; } = new();

    /// <summary>
    /// 外键信息
    /// </summary>
    public Dictionary<string, ForeignKeyInfo> ForeignKeys { get; set; } = new();
}

/// <summary>
/// 表信息
/// </summary>
public class TableInfo
{
    /// <summary>
    /// 表名
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 列信息
    /// </summary>
    public Dictionary<string, ColumnInfo> Columns { get; set; } = new();
}

/// <summary>
/// 列信息
/// </summary>
public class ColumnInfo
{
    /// <summary>
    /// 列名
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 数据类型
    /// </summary>
    public string DataType { get; set; } = string.Empty;

    /// <summary>
    /// 是否可空
    /// </summary>
    public bool IsNullable { get; set; }

    /// <summary>
    /// 是否主键
    /// </summary>
    public bool IsPrimaryKey { get; set; }

    /// <summary>
    /// 默认值
    /// </summary>
    public string? DefaultValue { get; set; }
}

/// <summary>
/// 索引信息
/// </summary>
public class IndexInfo
{
    /// <summary>
    /// 索引名
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 表名
    /// </summary>
    public string TableName { get; set; } = string.Empty;

    /// <summary>
    /// 列名列表
    /// </summary>
    public string[] Columns { get; set; } = Array.Empty<string>();

    /// <summary>
    /// 是否唯一
    /// </summary>
    public bool IsUnique { get; set; }
}

/// <summary>
/// 外键信息
/// </summary>
public class ForeignKeyInfo
{
    /// <summary>
    /// 外键名
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 源表
    /// </summary>
    public string SourceTable { get; set; } = string.Empty;

    /// <summary>
    /// 源列
    /// </summary>
    public string SourceColumn { get; set; } = string.Empty;

    /// <summary>
    /// 目标表
    /// </summary>
    public string TargetTable { get; set; } = string.Empty;

    /// <summary>
    /// 目标列
    /// </summary>
    public string TargetColumn { get; set; } = string.Empty;
}

/// <summary>
/// 查询结果
/// </summary>
public class QueryResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 结果数据
    /// </summary>
    public object? Data { get; set; }

    /// <summary>
    /// 受影响的行数
    /// </summary>
    public int AffectedRows { get; set; }

    /// <summary>
    /// 执行耗时（毫秒）
    /// </summary>
    public long ElapsedMilliseconds { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// 数据导出格式
/// </summary>
public enum DataExportFormat
{
    Json,
    Xml,
    Csv,
    Excel,
    Sql
}

/// <summary>
/// 问题严重程度
/// </summary>
public enum IssueSeverity
{
    Info,
    Warning,
    Error,
    Critical
}
