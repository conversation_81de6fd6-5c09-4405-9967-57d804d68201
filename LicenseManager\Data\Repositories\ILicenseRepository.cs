using LicenseManager.Models;

namespace LicenseManager.Data.Repositories;

/// <summary>
/// 许可证仓储接口
/// 提供许可证相关的专门查询和操作方法
/// </summary>
public interface ILicenseRepository : IRepository<License>
{
    /// <summary>
    /// 根据许可证密钥获取许可证
    /// </summary>
    /// <param name="licenseKey">许可证密钥</param>
    /// <returns>许可证对象</returns>
    Task<License?> GetByLicenseKeyAsync(string licenseKey);

    /// <summary>
    /// 根据应用程序ID获取许可证列表
    /// </summary>
    /// <param name="appId">应用程序ID</param>
    /// <returns>许可证列表</returns>
    Task<IEnumerable<License>> GetByAppIdAsync(string appId);

    /// <summary>
    /// 根据客户邮箱获取许可证列表
    /// </summary>
    /// <param name="customerEmail">客户邮箱</param>
    /// <returns>许可证列表</returns>
    Task<IEnumerable<License>> GetByCustomerEmailAsync(string customerEmail);

    /// <summary>
    /// 获取即将过期的许可证列表
    /// </summary>
    /// <param name="days">提前天数</param>
    /// <returns>即将过期的许可证列表</returns>
    Task<IEnumerable<License>> GetExpiringLicensesAsync(int days = 30);

    /// <summary>
    /// 获取已过期的许可证列表
    /// </summary>
    /// <returns>已过期的许可证列表</returns>
    Task<IEnumerable<License>> GetExpiredLicensesAsync();

    /// <summary>
    /// 获取活跃的许可证列表
    /// </summary>
    /// <returns>活跃的许可证列表</returns>
    Task<IEnumerable<License>> GetActiveLicensesAsync();

    /// <summary>
    /// 根据授权类型获取许可证列表
    /// </summary>
    /// <param name="authorizationTypeId">授权类型ID</param>
    /// <returns>许可证列表</returns>
    Task<IEnumerable<License>> GetByAuthorizationTypeAsync(int authorizationTypeId);

    /// <summary>
    /// 根据硬件指纹获取许可证
    /// </summary>
    /// <param name="hardwareFingerprint">硬件指纹</param>
    /// <returns>许可证对象</returns>
    Task<License?> GetByHardwareFingerprintAsync(string hardwareFingerprint);

    /// <summary>
    /// 获取许可证统计信息
    /// </summary>
    /// <returns>许可证统计信息</returns>
    Task<LicenseStatistics> GetStatisticsAsync();

    /// <summary>
    /// 搜索许可证
    /// </summary>
    /// <param name="searchTerm">搜索关键词</param>
    /// <param name="pageIndex">页索引</param>
    /// <param name="pageSize">页大小</param>
    /// <returns>搜索结果</returns>
    Task<PagedResult<License>> SearchLicensesAsync(string searchTerm, int pageIndex = 0, int pageSize = 20);

    /// <summary>
    /// 获取许可证详细信息（包含关联数据）
    /// </summary>
    /// <param name="id">许可证ID</param>
    /// <returns>许可证详细信息</returns>
    Task<License?> GetLicenseWithDetailsAsync(int id);

    /// <summary>
    /// 批量更新许可证状态
    /// </summary>
    /// <param name="licenseIds">许可证ID列表</param>
    /// <param name="isActive">新的激活状态</param>
    /// <returns>更新的许可证数量</returns>
    Task<int> UpdateLicenseStatusAsync(IEnumerable<int> licenseIds, bool isActive);

    /// <summary>
    /// 延长许可证有效期
    /// </summary>
    /// <param name="licenseId">许可证ID</param>
    /// <param name="additionalDays">延长天数</param>
    /// <returns>是否成功</returns>
    Task<bool> ExtendLicenseAsync(int licenseId, int additionalDays);

    /// <summary>
    /// 验证许可证是否有效
    /// </summary>
    /// <param name="licenseKey">许可证密钥</param>
    /// <param name="hardwareFingerprint">硬件指纹（可选）</param>
    /// <returns>验证结果</returns>
    Task<LicenseValidationResult> ValidateLicenseAsync(string licenseKey, string? hardwareFingerprint = null);

    /// <summary>
    /// 获取许可证使用历史
    /// </summary>
    /// <param name="licenseId">许可证ID</param>
    /// <returns>使用历史记录</returns>
    Task<IEnumerable<LicenseUsageHistory>> GetLicenseUsageHistoryAsync(int licenseId);
}

/// <summary>
/// 许可证统计信息
/// </summary>
public class LicenseStatistics
{
    /// <summary>
    /// 总许可证数量
    /// </summary>
    public int TotalLicenses { get; set; }

    /// <summary>
    /// 活跃许可证数量
    /// </summary>
    public int ActiveLicenses { get; set; }

    /// <summary>
    /// 已过期许可证数量
    /// </summary>
    public int ExpiredLicenses { get; set; }

    /// <summary>
    /// 即将过期许可证数量（30天内）
    /// </summary>
    public int ExpiringLicenses { get; set; }

    /// <summary>
    /// 试用版许可证数量
    /// </summary>
    public int TrialLicenses { get; set; }

    /// <summary>
    /// 本月新增许可证数量
    /// </summary>
    public int NewLicensesThisMonth { get; set; }

    /// <summary>
    /// 按授权类型分组的统计
    /// </summary>
    public Dictionary<string, int> LicensesByType { get; set; } = new();

    /// <summary>
    /// 按应用程序分组的统计
    /// </summary>
    public Dictionary<string, int> LicensesByApp { get; set; } = new();
}

/// <summary>
/// 许可证验证结果
/// </summary>
public class LicenseValidationResult
{
    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 许可证对象
    /// </summary>
    public License? License { get; set; }

    /// <summary>
    /// 验证消息
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 错误代码
    /// </summary>
    public string? ErrorCode { get; set; }

    /// <summary>
    /// 剩余天数
    /// </summary>
    public int? RemainingDays { get; set; }

    /// <summary>
    /// 硬件指纹匹配度
    /// </summary>
    public decimal? HardwareFingerprintMatch { get; set; }
}

/// <summary>
/// 许可证使用历史
/// </summary>
public class LicenseUsageHistory
{
    /// <summary>
    /// 历史记录ID
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// 许可证ID
    /// </summary>
    public int LicenseId { get; set; }

    /// <summary>
    /// 操作类型
    /// </summary>
    public string OperationType { get; set; } = string.Empty;

    /// <summary>
    /// 操作描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 操作时间
    /// </summary>
    public DateTime OperationTime { get; set; }

    /// <summary>
    /// 操作者
    /// </summary>
    public string? Operator { get; set; }

    /// <summary>
    /// 客户端信息
    /// </summary>
    public string? ClientInfo { get; set; }

    /// <summary>
    /// IP地址
    /// </summary>
    public string? IpAddress { get; set; }
}
