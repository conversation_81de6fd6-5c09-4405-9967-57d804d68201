using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace LicenseManager.Models;

/// <summary>
/// 许可证信息实体模型
/// 存储许可证的扩展属性和自定义信息
/// </summary>
[Table("LicenseInfos")]
public class LicenseInfo
{
    /// <summary>
    /// 许可证信息唯一标识符
    /// </summary>
    [Key]
    public int Id { get; set; }

    /// <summary>
    /// 关联的许可证ID
    /// </summary>
    [Required]
    public int LicenseId { get; set; }

    /// <summary>
    /// 属性名称
    /// </summary>
    [Required]
    [StringLength(100)]
    public string PropertyName { get; set; } = string.Empty;

    /// <summary>
    /// 属性值
    /// </summary>
    [Required]
    [StringLength(2000)]
    public string PropertyValue { get; set; } = string.Empty;

    /// <summary>
    /// 属性类型（用于类型转换和验证）
    /// </summary>
    [StringLength(50)]
    public string? PropertyType { get; set; } = "String";

    /// <summary>
    /// 属性描述
    /// </summary>
    [StringLength(500)]
    public string? Description { get; set; }

    /// <summary>
    /// 属性分组（用于界面显示分组）
    /// </summary>
    [StringLength(100)]
    public string? PropertyGroup { get; set; }

    /// <summary>
    /// 是否为敏感信息（敏感信息在界面上会被遮蔽）
    /// </summary>
    [Required]
    public bool IsSensitive { get; set; } = false;

    /// <summary>
    /// 是否为只读属性
    /// </summary>
    [Required]
    public bool IsReadOnly { get; set; } = false;

    /// <summary>
    /// 是否为必需属性
    /// </summary>
    [Required]
    public bool IsRequired { get; set; } = false;

    /// <summary>
    /// 属性验证规则（正则表达式或其他验证规则）
    /// </summary>
    [StringLength(500)]
    public string? ValidationRule { get; set; }

    /// <summary>
    /// 默认值
    /// </summary>
    [StringLength(500)]
    public string? DefaultValue { get; set; }

    /// <summary>
    /// 排序顺序
    /// </summary>
    [Required]
    public int SortOrder { get; set; } = 0;

    /// <summary>
    /// 是否在许可证文件中包含此属性
    /// </summary>
    [Required]
    public bool IncludeInLicense { get; set; } = true;

    /// <summary>
    /// 是否在许可证验证时检查此属性
    /// </summary>
    [Required]
    public bool ValidateOnCheck { get; set; } = false;

    /// <summary>
    /// 属性标签（用于界面显示）
    /// </summary>
    [StringLength(200)]
    public string? DisplayLabel { get; set; }

    /// <summary>
    /// 属性提示信息
    /// </summary>
    [StringLength(500)]
    public string? HelpText { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    [Required]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    // 导航属性
    /// <summary>
    /// 关联的许可证
    /// </summary>
    [ForeignKey(nameof(LicenseId))]
    public virtual License? License { get; set; }

    /// <summary>
    /// 计算属性：显示标签（如果没有设置DisplayLabel则使用PropertyName）
    /// </summary>
    [NotMapped]
    public string Label => !string.IsNullOrEmpty(DisplayLabel) ? DisplayLabel : PropertyName;

    /// <summary>
    /// 计算属性：显示值（敏感信息会被遮蔽）
    /// </summary>
    [NotMapped]
    public string DisplayValue
    {
        get
        {
            if (IsSensitive && !string.IsNullOrEmpty(PropertyValue))
            {
                return new string('*', Math.Min(PropertyValue.Length, 8));
            }
            return PropertyValue;
        }
    }

    /// <summary>
    /// 计算属性：属性类型枚举
    /// </summary>
    [NotMapped]
    public PropertyTypeEnum PropertyTypeEnum
    {
        get
        {
            return PropertyType?.ToLower() switch
            {
                "string" => PropertyTypeEnum.String,
                "int" or "integer" => PropertyTypeEnum.Integer,
                "bool" or "boolean" => PropertyTypeEnum.Boolean,
                "datetime" or "date" => PropertyTypeEnum.DateTime,
                "decimal" or "double" or "float" => PropertyTypeEnum.Decimal,
                "json" => PropertyTypeEnum.Json,
                "url" => PropertyTypeEnum.Url,
                "email" => PropertyTypeEnum.Email,
                _ => PropertyTypeEnum.String
            };
        }
    }

    /// <summary>
    /// 获取强类型的属性值
    /// </summary>
    /// <typeparam name="T">目标类型</typeparam>
    /// <returns>转换后的值</returns>
    public T? GetTypedValue<T>()
    {
        if (string.IsNullOrEmpty(PropertyValue))
            return default(T);

        try
        {
            return (T)Convert.ChangeType(PropertyValue, typeof(T));
        }
        catch
        {
            return default(T);
        }
    }

    /// <summary>
    /// 验证属性值是否符合验证规则
    /// </summary>
    /// <returns>验证结果</returns>
    public bool ValidateValue()
    {
        if (IsRequired && string.IsNullOrEmpty(PropertyValue))
            return false;

        if (!string.IsNullOrEmpty(ValidationRule) && !string.IsNullOrEmpty(PropertyValue))
        {
            try
            {
                return System.Text.RegularExpressions.Regex.IsMatch(PropertyValue, ValidationRule);
            }
            catch
            {
                return false;
            }
        }

        return true;
    }
}

/// <summary>
/// 属性类型枚举
/// </summary>
public enum PropertyTypeEnum
{
    String,
    Integer,
    Boolean,
    DateTime,
    Decimal,
    Json,
    Url,
    Email
}
