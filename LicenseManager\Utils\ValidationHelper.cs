using System.ComponentModel.DataAnnotations;
using System.Net.Mail;
using System.Text.RegularExpressions;

namespace LicenseManager.Utils;

/// <summary>
/// 验证工具类
/// 提供各种数据验证功能的静态方法
/// </summary>
public static class ValidationHelper
{
    /// <summary>
    /// 验证邮箱地址
    /// </summary>
    /// <param name="email">邮箱地址</param>
    /// <returns>是否有效</returns>
    public static bool IsValidEmail(string? email)
    {
        if (string.IsNullOrWhiteSpace(email))
        {
            return false;
        }

        try
        {
            var mailAddress = new MailAddress(email);
            return mailAddress.Address == email;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 验证URL
    /// </summary>
    /// <param name="url">URL地址</param>
    /// <returns>是否有效</returns>
    public static bool IsValidUrl(string? url)
    {
        if (string.IsNullOrWhiteSpace(url))
        {
            return false;
        }

        return Uri.TryCreate(url, UriKind.Absolute, out var result) &&
               (result.Scheme == Uri.UriSchemeHttp || result.Scheme == Uri.UriSchemeHttps);
    }

    /// <summary>
    /// 验证IP地址
    /// </summary>
    /// <param name="ipAddress">IP地址</param>
    /// <returns>是否有效</returns>
    public static bool IsValidIpAddress(string? ipAddress)
    {
        if (string.IsNullOrWhiteSpace(ipAddress))
        {
            return false;
        }

        return System.Net.IPAddress.TryParse(ipAddress, out _);
    }

    /// <summary>
    /// 验证MAC地址
    /// </summary>
    /// <param name="macAddress">MAC地址</param>
    /// <returns>是否有效</returns>
    public static bool IsValidMacAddress(string? macAddress)
    {
        if (string.IsNullOrWhiteSpace(macAddress))
        {
            return false;
        }

        var pattern = @"^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$";
        return Regex.IsMatch(macAddress, pattern);
    }

    /// <summary>
    /// 验证GUID
    /// </summary>
    /// <param name="guid">GUID字符串</param>
    /// <returns>是否有效</returns>
    public static bool IsValidGuid(string? guid)
    {
        if (string.IsNullOrWhiteSpace(guid))
        {
            return false;
        }

        return Guid.TryParse(guid, out _);
    }

    /// <summary>
    /// 验证Base64字符串
    /// </summary>
    /// <param name="base64String">Base64字符串</param>
    /// <returns>是否有效</returns>
    public static bool IsValidBase64(string? base64String)
    {
        if (string.IsNullOrWhiteSpace(base64String))
        {
            return false;
        }

        try
        {
            Convert.FromBase64String(base64String);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 验证十六进制字符串
    /// </summary>
    /// <param name="hexString">十六进制字符串</param>
    /// <returns>是否有效</returns>
    public static bool IsValidHexString(string? hexString)
    {
        if (string.IsNullOrWhiteSpace(hexString))
        {
            return false;
        }

        var pattern = @"^[0-9A-Fa-f]+$";
        return Regex.IsMatch(hexString, pattern) && hexString.Length % 2 == 0;
    }

    /// <summary>
    /// 验证许可证密钥格式
    /// </summary>
    /// <param name="licenseKey">许可证密钥</param>
    /// <returns>是否有效</returns>
    public static bool IsValidLicenseKey(string? licenseKey)
    {
        if (string.IsNullOrWhiteSpace(licenseKey))
        {
            return false;
        }

        // 支持多种许可证密钥格式
        var patterns = new[]
        {
            @"^[A-Z]{3}-[A-Z0-9]{5}-[A-Z0-9]{5}-[A-Z0-9]{5}-[A-Z0-9]{5}$", // LIC-XXXXX-XXXXX-XXXXX-XXXXX
            @"^[A-Z0-9]{8}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{12}$", // GUID格式
            @"^[A-Z0-9]{25,50}$" // 简单字母数字组合
        };

        return patterns.Any(pattern => Regex.IsMatch(licenseKey.ToUpper(), pattern));
    }

    /// <summary>
    /// 验证版本号
    /// </summary>
    /// <param name="version">版本号</param>
    /// <returns>是否有效</returns>
    public static bool IsValidVersion(string? version)
    {
        if (string.IsNullOrWhiteSpace(version))
        {
            return false;
        }

        return Version.TryParse(version, out _);
    }

    /// <summary>
    /// 验证文件路径
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>是否有效</returns>
    public static bool IsValidFilePath(string? filePath)
    {
        if (string.IsNullOrWhiteSpace(filePath))
        {
            return false;
        }

        try
        {
            Path.GetFullPath(filePath);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 验证JSON字符串
    /// </summary>
    /// <param name="json">JSON字符串</param>
    /// <returns>是否有效</returns>
    public static bool IsValidJson(string? json)
    {
        if (string.IsNullOrWhiteSpace(json))
        {
            return false;
        }

        try
        {
            System.Text.Json.JsonDocument.Parse(json);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 验证XML字符串
    /// </summary>
    /// <param name="xml">XML字符串</param>
    /// <returns>是否有效</returns>
    public static bool IsValidXml(string? xml)
    {
        if (string.IsNullOrWhiteSpace(xml))
        {
            return false;
        }

        try
        {
            var doc = new System.Xml.XmlDocument();
            doc.LoadXml(xml);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 验证正则表达式
    /// </summary>
    /// <param name="pattern">正则表达式模式</param>
    /// <returns>是否有效</returns>
    public static bool IsValidRegexPattern(string? pattern)
    {
        if (string.IsNullOrWhiteSpace(pattern))
        {
            return false;
        }

        try
        {
            Regex.Match("", pattern);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 验证数字范围
    /// </summary>
    /// <param name="value">数值</param>
    /// <param name="min">最小值</param>
    /// <param name="max">最大值</param>
    /// <returns>是否在范围内</returns>
    public static bool IsInRange(int value, int min, int max)
    {
        return value >= min && value <= max;
    }

    /// <summary>
    /// 验证数字范围（双精度）
    /// </summary>
    /// <param name="value">数值</param>
    /// <param name="min">最小值</param>
    /// <param name="max">最大值</param>
    /// <returns>是否在范围内</returns>
    public static bool IsInRange(double value, double min, double max)
    {
        return value >= min && value <= max;
    }

    /// <summary>
    /// 验证字符串长度
    /// </summary>
    /// <param name="value">字符串</param>
    /// <param name="minLength">最小长度</param>
    /// <param name="maxLength">最大长度</param>
    /// <returns>是否在长度范围内</returns>
    public static bool IsValidLength(string? value, int minLength, int maxLength)
    {
        if (value == null)
        {
            return minLength == 0;
        }

        return value.Length >= minLength && value.Length <= maxLength;
    }

    /// <summary>
    /// 验证日期范围
    /// </summary>
    /// <param name="date">日期</param>
    /// <param name="minDate">最小日期</param>
    /// <param name="maxDate">最大日期</param>
    /// <returns>是否在日期范围内</returns>
    public static bool IsInDateRange(DateTime date, DateTime minDate, DateTime maxDate)
    {
        return date >= minDate && date <= maxDate;
    }

    /// <summary>
    /// 验证对象属性
    /// </summary>
    /// <param name="obj">要验证的对象</param>
    /// <returns>验证结果</returns>
    public static ObjectValidationResult ValidateObject(object obj)
    {
        var result = new ObjectValidationResult();
        var context = new ValidationContext(obj);
        var validationResults = new List<ValidationResult>();

        result.IsValid = Validator.TryValidateObject(obj, context, validationResults, true);
        result.ValidationErrors = validationResults.Select(vr => new ValidationError
        {
            PropertyName = vr.MemberNames.FirstOrDefault() ?? "",
            ErrorMessage = vr.ErrorMessage ?? "",
            AttemptedValue = GetPropertyValue(obj, vr.MemberNames.FirstOrDefault())
        }).ToList();

        return result;
    }

    /// <summary>
    /// 验证属性值
    /// </summary>
    /// <param name="obj">对象</param>
    /// <param name="propertyName">属性名</param>
    /// <param name="value">属性值</param>
    /// <returns>验证结果</returns>
    public static PropertyValidationResult ValidateProperty(object obj, string propertyName, object? value)
    {
        var result = new PropertyValidationResult();
        var context = new ValidationContext(obj) { MemberName = propertyName };
        var validationResults = new List<ValidationResult>();

        result.IsValid = Validator.TryValidateProperty(value, context, validationResults);
        result.ValidationErrors = validationResults.Select(vr => new ValidationError
        {
            PropertyName = propertyName,
            ErrorMessage = vr.ErrorMessage ?? "",
            AttemptedValue = value
        }).ToList();

        return result;
    }

    /// <summary>
    /// 验证必需字段
    /// </summary>
    /// <param name="value">字段值</param>
    /// <param name="fieldName">字段名</param>
    /// <returns>验证结果</returns>
    public static FieldValidationResult ValidateRequired(object? value, string fieldName)
    {
        var result = new FieldValidationResult { FieldName = fieldName };

        if (value == null)
        {
            result.IsValid = false;
            result.ErrorMessage = $"{fieldName}是必需的";
            return result;
        }

        if (value is string stringValue && string.IsNullOrWhiteSpace(stringValue))
        {
            result.IsValid = false;
            result.ErrorMessage = $"{fieldName}不能为空";
            return result;
        }

        result.IsValid = true;
        return result;
    }

    /// <summary>
    /// 验证字符串格式
    /// </summary>
    /// <param name="value">字符串值</param>
    /// <param name="pattern">正则表达式模式</param>
    /// <param name="fieldName">字段名</param>
    /// <param name="errorMessage">错误消息</param>
    /// <returns>验证结果</returns>
    public static FieldValidationResult ValidateFormat(string? value, string pattern, string fieldName, string? errorMessage = null)
    {
        var result = new FieldValidationResult { FieldName = fieldName };

        if (string.IsNullOrEmpty(value))
        {
            result.IsValid = true; // 空值由Required验证处理
            return result;
        }

        if (!Regex.IsMatch(value, pattern))
        {
            result.IsValid = false;
            result.ErrorMessage = errorMessage ?? $"{fieldName}格式不正确";
            return result;
        }

        result.IsValid = true;
        return result;
    }

    /// <summary>
    /// 批量验证字段
    /// </summary>
    /// <param name="validations">验证函数列表</param>
    /// <returns>批量验证结果</returns>
    public static BatchValidationResult ValidateBatch(params Func<FieldValidationResult>[] validations)
    {
        var result = new BatchValidationResult();
        var errors = new List<FieldValidationResult>();

        foreach (var validation in validations)
        {
            var fieldResult = validation();
            if (!fieldResult.IsValid)
            {
                errors.Add(fieldResult);
            }
        }

        result.IsValid = errors.Count == 0;
        result.FieldErrors = errors;
        result.ErrorCount = errors.Count;

        return result;
    }

    /// <summary>
    /// 验证硬件指纹格式
    /// </summary>
    /// <param name="fingerprint">硬件指纹</param>
    /// <returns>是否有效</returns>
    public static bool IsValidHardwareFingerprint(string? fingerprint)
    {
        if (string.IsNullOrWhiteSpace(fingerprint))
        {
            return false;
        }

        // 硬件指纹通常是SHA256哈希值
        return IsValidHexString(fingerprint) && fingerprint.Length == 64;
    }

    /// <summary>
    /// 验证许可证有效期
    /// </summary>
    /// <param name="issueDate">颁发日期</param>
    /// <param name="expiryDate">过期日期</param>
    /// <returns>验证结果</returns>
    public static LicenseDateValidationResult ValidateLicenseDates(DateTime issueDate, DateTime expiryDate)
    {
        var result = new LicenseDateValidationResult();

        if (issueDate > DateTime.UtcNow.AddDays(1)) // 允许1天的时间差
        {
            result.IsValid = false;
            result.ErrorMessage = "许可证颁发日期不能是未来时间";
            return result;
        }

        if (expiryDate <= issueDate)
        {
            result.IsValid = false;
            result.ErrorMessage = "许可证过期日期必须晚于颁发日期";
            return result;
        }

        result.IsValid = true;
        result.IsExpired = DateTime.UtcNow > expiryDate;
        result.DaysRemaining = Math.Max(0, (expiryDate - DateTime.UtcNow).Days);

        return result;
    }

    /// <summary>
    /// 获取对象属性值
    /// </summary>
    /// <param name="obj">对象</param>
    /// <param name="propertyName">属性名</param>
    /// <returns>属性值</returns>
    private static object? GetPropertyValue(object obj, string? propertyName)
    {
        if (string.IsNullOrEmpty(propertyName))
        {
            return null;
        }

        try
        {
            var property = obj.GetType().GetProperty(propertyName);
            return property?.GetValue(obj);
        }
        catch
        {
            return null;
        }
    }
}

/// <summary>
/// 对象验证结果
/// </summary>
public class ObjectValidationResult
{
    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 验证错误列表
    /// </summary>
    public List<ValidationError> ValidationErrors { get; set; } = new();

    /// <summary>
    /// 错误数量
    /// </summary>
    public int ErrorCount => ValidationErrors.Count;
}

/// <summary>
/// 属性验证结果
/// </summary>
public class PropertyValidationResult
{
    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 验证错误列表
    /// </summary>
    public List<ValidationError> ValidationErrors { get; set; } = new();
}

/// <summary>
/// 字段验证结果
/// </summary>
public class FieldValidationResult
{
    /// <summary>
    /// 字段名
    /// </summary>
    public string FieldName { get; set; } = string.Empty;

    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// 批量验证结果
/// </summary>
public class BatchValidationResult
{
    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 字段错误列表
    /// </summary>
    public List<FieldValidationResult> FieldErrors { get; set; } = new();

    /// <summary>
    /// 错误数量
    /// </summary>
    public int ErrorCount { get; set; }
}

/// <summary>
/// 验证错误
/// </summary>
public class ValidationError
{
    /// <summary>
    /// 属性名
    /// </summary>
    public string PropertyName { get; set; } = string.Empty;

    /// <summary>
    /// 错误消息
    /// </summary>
    public string ErrorMessage { get; set; } = string.Empty;

    /// <summary>
    /// 尝试的值
    /// </summary>
    public object? AttemptedValue { get; set; }
}

/// <summary>
/// 许可证日期验证结果
/// </summary>
public class LicenseDateValidationResult
{
    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 是否已过期
    /// </summary>
    public bool IsExpired { get; set; }

    /// <summary>
    /// 剩余天数
    /// </summary>
    public int DaysRemaining { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }
}
