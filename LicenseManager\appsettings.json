{"ConnectionStrings": {"DefaultConnection": "Data Source=LicenseManager.db"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "File", "Args": {"path": "Logs/license-manager-.log", "rollingInterval": "Day", "retainedFileCountLimit": 30, "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}"}}]}, "LicenseSettings": {"DefaultValidityDays": 365, "MaxLicenseCount": 1000, "EncryptionKeySize": 2048, "AllowedApplications": ["TestApp1", "TestApp2"]}, "UI": {"Theme": "Light", "Language": "zh-CN", "WindowSize": {"Width": 1200, "Height": 800}, "GridPageSize": 50}}