namespace LicenseManager.Services;

/// <summary>
/// 加密服务接口
/// 提供RSA、AES等加密算法的统一接口
/// </summary>
public interface IEncryptionService
{
    /// <summary>
    /// 生成RSA密钥对
    /// </summary>
    /// <param name="keySize">密钥长度（位）</param>
    /// <returns>RSA密钥对</returns>
    RsaKeyPair GenerateRsaKeyPair(int keySize = 2048);

    /// <summary>
    /// RSA加密
    /// </summary>
    /// <param name="data">要加密的数据</param>
    /// <param name="publicKey">公钥</param>
    /// <returns>加密后的数据</returns>
    byte[] RsaEncrypt(byte[] data, string publicKey);

    /// <summary>
    /// RSA解密
    /// </summary>
    /// <param name="encryptedData">加密的数据</param>
    /// <param name="privateKey">私钥</param>
    /// <returns>解密后的数据</returns>
    byte[] RsaDecrypt(byte[] encryptedData, string privateKey);

    /// <summary>
    /// RSA签名
    /// </summary>
    /// <param name="data">要签名的数据</param>
    /// <param name="privateKey">私钥</param>
    /// <returns>签名</returns>
    byte[] RsaSign(byte[] data, string privateKey);

    /// <summary>
    /// RSA验证签名
    /// </summary>
    /// <param name="data">原始数据</param>
    /// <param name="signature">签名</param>
    /// <param name="publicKey">公钥</param>
    /// <returns>验证结果</returns>
    bool RsaVerifySignature(byte[] data, byte[] signature, string publicKey);

    /// <summary>
    /// 生成AES密钥
    /// </summary>
    /// <param name="keySize">密钥长度（位）</param>
    /// <returns>AES密钥</returns>
    AesKey GenerateAesKey(int keySize = 256);

    /// <summary>
    /// AES加密
    /// </summary>
    /// <param name="data">要加密的数据</param>
    /// <param name="key">密钥</param>
    /// <param name="iv">初始化向量</param>
    /// <returns>加密后的数据</returns>
    byte[] AesEncrypt(byte[] data, byte[] key, byte[] iv);

    /// <summary>
    /// AES解密
    /// </summary>
    /// <param name="encryptedData">加密的数据</param>
    /// <param name="key">密钥</param>
    /// <param name="iv">初始化向量</param>
    /// <returns>解密后的数据</returns>
    byte[] AesDecrypt(byte[] encryptedData, byte[] key, byte[] iv);

    /// <summary>
    /// 计算SHA256哈希
    /// </summary>
    /// <param name="data">要计算哈希的数据</param>
    /// <returns>哈希值</returns>
    byte[] ComputeSha256Hash(byte[] data);

    /// <summary>
    /// 计算MD5哈希
    /// </summary>
    /// <param name="data">要计算哈希的数据</param>
    /// <returns>哈希值</returns>
    byte[] ComputeMd5Hash(byte[] data);

    /// <summary>
    /// 生成随机字节
    /// </summary>
    /// <param name="length">字节长度</param>
    /// <returns>随机字节数组</returns>
    byte[] GenerateRandomBytes(int length);

    /// <summary>
    /// 生成随机字符串
    /// </summary>
    /// <param name="length">字符串长度</param>
    /// <param name="includeSpecialChars">是否包含特殊字符</param>
    /// <returns>随机字符串</returns>
    string GenerateRandomString(int length, bool includeSpecialChars = false);

    /// <summary>
    /// Base64编码
    /// </summary>
    /// <param name="data">要编码的数据</param>
    /// <returns>Base64字符串</returns>
    string ToBase64String(byte[] data);

    /// <summary>
    /// Base64解码
    /// </summary>
    /// <param name="base64String">Base64字符串</param>
    /// <returns>解码后的数据</returns>
    byte[] FromBase64String(string base64String);

    /// <summary>
    /// 十六进制编码
    /// </summary>
    /// <param name="data">要编码的数据</param>
    /// <returns>十六进制字符串</returns>
    string ToHexString(byte[] data);

    /// <summary>
    /// 十六进制解码
    /// </summary>
    /// <param name="hexString">十六进制字符串</param>
    /// <returns>解码后的数据</returns>
    byte[] FromHexString(string hexString);

    /// <summary>
    /// 密码哈希（使用PBKDF2）
    /// </summary>
    /// <param name="password">密码</param>
    /// <param name="salt">盐值</param>
    /// <param name="iterations">迭代次数</param>
    /// <param name="hashLength">哈希长度</param>
    /// <returns>密码哈希</returns>
    byte[] HashPassword(string password, byte[] salt, int iterations = 10000, int hashLength = 32);

    /// <summary>
    /// 验证密码
    /// </summary>
    /// <param name="password">密码</param>
    /// <param name="hash">存储的哈希</param>
    /// <param name="salt">盐值</param>
    /// <param name="iterations">迭代次数</param>
    /// <returns>验证结果</returns>
    bool VerifyPassword(string password, byte[] hash, byte[] salt, int iterations = 10000);

    /// <summary>
    /// 加密许可证数据
    /// </summary>
    /// <param name="licenseData">许可证数据</param>
    /// <param name="publicKey">公钥</param>
    /// <returns>加密结果</returns>
    EncryptionResult EncryptLicenseData(LicenseData licenseData, string publicKey);

    /// <summary>
    /// 解密许可证数据
    /// </summary>
    /// <param name="encryptedData">加密的数据</param>
    /// <param name="privateKey">私钥</param>
    /// <returns>解密结果</returns>
    DecryptionResult<LicenseData> DecryptLicenseData(byte[] encryptedData, string privateKey);

    /// <summary>
    /// 验证许可证完整性
    /// </summary>
    /// <param name="licenseData">许可证数据</param>
    /// <param name="signature">签名</param>
    /// <param name="publicKey">公钥</param>
    /// <returns>验证结果</returns>
    bool VerifyLicenseIntegrity(LicenseData licenseData, byte[] signature, string publicKey);
}

/// <summary>
/// RSA密钥对
/// </summary>
public class RsaKeyPair
{
    /// <summary>
    /// 公钥
    /// </summary>
    public string PublicKey { get; set; } = string.Empty;

    /// <summary>
    /// 私钥
    /// </summary>
    public string PrivateKey { get; set; } = string.Empty;

    /// <summary>
    /// 密钥长度
    /// </summary>
    public int KeySize { get; set; }

    /// <summary>
    /// 生成时间
    /// </summary>
    public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// AES密钥
/// </summary>
public class AesKey
{
    /// <summary>
    /// 密钥
    /// </summary>
    public byte[] Key { get; set; } = Array.Empty<byte>();

    /// <summary>
    /// 初始化向量
    /// </summary>
    public byte[] IV { get; set; } = Array.Empty<byte>();

    /// <summary>
    /// 密钥长度
    /// </summary>
    public int KeySize { get; set; }

    /// <summary>
    /// 生成时间
    /// </summary>
    public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 加密结果
/// </summary>
public class EncryptionResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 加密后的数据
    /// </summary>
    public byte[]? EncryptedData { get; set; }

    /// <summary>
    /// 签名
    /// </summary>
    public byte[]? Signature { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 加密算法
    /// </summary>
    public string? Algorithm { get; set; }

    /// <summary>
    /// 加密时间
    /// </summary>
    public DateTime EncryptedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 解密结果
/// </summary>
/// <typeparam name="T">解密后的数据类型</typeparam>
public class DecryptionResult<T>
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 解密后的数据
    /// </summary>
    public T? Data { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 是否验证了完整性
    /// </summary>
    public bool IntegrityVerified { get; set; }

    /// <summary>
    /// 解密时间
    /// </summary>
    public DateTime DecryptedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 许可证数据（用于加密/解密）
/// </summary>
public class LicenseData
{
    /// <summary>
    /// 应用程序ID
    /// </summary>
    public string AppId { get; set; } = string.Empty;

    /// <summary>
    /// 应用程序名称
    /// </summary>
    public string AppName { get; set; } = string.Empty;

    /// <summary>
    /// 许可证ID
    /// </summary>
    public int LicenseId { get; set; }

    /// <summary>
    /// 授权类型
    /// </summary>
    public string AuthorizationType { get; set; } = string.Empty;

    /// <summary>
    /// 客户信息
    /// </summary>
    public string? CustomerName { get; set; }

    /// <summary>
    /// 客户邮箱
    /// </summary>
    public string? CustomerEmail { get; set; }

    /// <summary>
    /// 颁发日期
    /// </summary>
    public DateTime IssueDate { get; set; }

    /// <summary>
    /// 过期日期
    /// </summary>
    public DateTime ExpiryDate { get; set; }

    /// <summary>
    /// 最大用户数
    /// </summary>
    public int MaxUsers { get; set; }

    /// <summary>
    /// 硬件指纹
    /// </summary>
    public string? HardwareFingerprint { get; set; }

    /// <summary>
    /// 功能特性
    /// </summary>
    public string[]? Features { get; set; }

    /// <summary>
    /// 自定义属性
    /// </summary>
    public Dictionary<string, string>? CustomProperties { get; set; }

    /// <summary>
    /// 版本号
    /// </summary>
    public int Version { get; set; } = 1;

    /// <summary>
    /// 校验和
    /// </summary>
    public string? Checksum { get; set; }
}
