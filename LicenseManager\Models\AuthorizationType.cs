using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace LicenseManager.Models;

/// <summary>
/// 授权类型实体模型
/// 定义不同的许可证授权类型（如试用版、标准版、专业版等）
/// </summary>
[Table("AuthorizationTypes")]
public class AuthorizationType
{
    /// <summary>
    /// 授权类型唯一标识符
    /// </summary>
    [Key]
    public int Id { get; set; }

    /// <summary>
    /// 授权类型名称
    /// </summary>
    [Required]
    [StringLength(100)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 授权类型描述
    /// </summary>
    [StringLength(500)]
    public string? Description { get; set; }

    /// <summary>
    /// 最大用户数量（-1表示无限制）
    /// </summary>
    [Required]
    public int MaxUsers { get; set; } = 1;

    /// <summary>
    /// 功能特性列表（JSON格式存储）
    /// 例如：["feature1", "feature2", "feature3"]
    /// </summary>
    [StringLength(2000)]
    public string? Features { get; set; }

    /// <summary>
    /// 默认有效期天数
    /// </summary>
    [Required]
    public int ValidityDays { get; set; } = 365;

    /// <summary>
    /// 是否允许硬件绑定
    /// </summary>
    [Required]
    public bool AllowHardwareBinding { get; set; } = true;

    /// <summary>
    /// 是否允许网络验证
    /// </summary>
    [Required]
    public bool AllowNetworkValidation { get; set; } = false;

    /// <summary>
    /// 最大激活次数（-1表示无限制）
    /// </summary>
    [Required]
    public int MaxActivations { get; set; } = 1;

    /// <summary>
    /// 授权类型优先级（数字越大优先级越高）
    /// </summary>
    [Required]
    public int Priority { get; set; } = 0;

    /// <summary>
    /// 是否为试用版
    /// </summary>
    [Required]
    public bool IsTrial { get; set; } = false;

    /// <summary>
    /// 试用期天数（仅当IsTrial为true时有效）
    /// </summary>
    public int? TrialDays { get; set; }

    /// <summary>
    /// 价格（用于显示，不参与业务逻辑）
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal? Price { get; set; }

    /// <summary>
    /// 货币单位
    /// </summary>
    [StringLength(10)]
    public string? Currency { get; set; } = "CNY";

    /// <summary>
    /// 是否启用
    /// </summary>
    [Required]
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 排序顺序
    /// </summary>
    [Required]
    public int SortOrder { get; set; } = 0;

    /// <summary>
    /// 创建时间
    /// </summary>
    [Required]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 最后更新时间
    /// </summary>
    [Required]
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    // 导航属性
    /// <summary>
    /// 使用此授权类型的许可证列表
    /// </summary>
    public virtual ICollection<License> Licenses { get; set; } = new List<License>();

    /// <summary>
    /// 计算属性：功能特性数量
    /// </summary>
    [NotMapped]
    public int FeatureCount
    {
        get
        {
            if (string.IsNullOrEmpty(Features)) return 0;
            try
            {
                var features = System.Text.Json.JsonSerializer.Deserialize<string[]>(Features);
                return features?.Length ?? 0;
            }
            catch
            {
                return 0;
            }
        }
    }

    /// <summary>
    /// 计算属性：授权类型显示名称
    /// </summary>
    [NotMapped]
    public string DisplayName
    {
        get
        {
            var displayName = Name;
            if (IsTrial)
            {
                displayName += " (试用版)";
            }
            if (!IsEnabled)
            {
                displayName += " (已禁用)";
            }
            return displayName;
        }
    }

    /// <summary>
    /// 计算属性：价格显示文本
    /// </summary>
    [NotMapped]
    public string PriceDisplay
    {
        get
        {
            if (Price.HasValue)
            {
                return $"{Price:F2} {Currency}";
            }
            return IsTrial ? "免费试用" : "价格面议";
        }
    }
}
