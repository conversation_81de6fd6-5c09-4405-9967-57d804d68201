{"runtimeOptions": {"tfm": "net9.0", "frameworks": [{"name": "Microsoft.NETCore.App", "version": "9.0.4"}, {"name": "Microsoft.WindowsDesktop.App", "version": "9.0.4"}], "additionalProbingPaths": ["C:\\Users\\<USER>\\.dotnet\\store\\|arch|\\|tfm|", "C:\\Users\\<USER>\\.nuget\\packages", "D:\\Microsoft Visual Studio\\Shared\\NuGetPackages", "D:\\Syncfusion\\Essential Studio\\WPF\\29.1.33\\ToolboxNuGetPackages"], "configProperties": {"MVVMTOOLKIT_ENABLE_INOTIFYPROPERTYCHANGING_SUPPORT": true, "System.Reflection.NullabilityInfoContext.IsSupported": true, "System.Runtime.Serialization.EnableUnsafeBinaryFormatterSerialization": false, "CSWINRT_USE_WINDOWS_UI_XAML_PROJECTIONS": false, "Microsoft.NETCore.DotNetHostPolicy.SetAppPaths": true}}}