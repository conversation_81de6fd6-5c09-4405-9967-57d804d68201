<?xml version="1.0"?>
<doc>
    <assembly>
        <name>LicenseManager</name>
    </assembly>
    <members>
        <member name="T:LicenseManager.App">
            <summary>
            Interaction logic for App.xaml
            </summary>
            <summary>
            App
            </summary>
        </member>
        <member name="M:LicenseManager.App.InitializeComponent">
            <summary>
            InitializeComponent
            </summary>
        </member>
        <member name="M:LicenseManager.App.Main">
            <summary>
            Application Entry Point.
            </summary>
        </member>
        <member name="T:LicenseManager.Data.LicenseDbContext">
            <summary>
            许可证管理系统数据库上下文
            </summary>
        </member>
        <member name="M:LicenseManager.Data.LicenseDbContext.#ctor(Microsoft.EntityFrameworkCore.DbContextOptions{LicenseManager.Data.LicenseDbContext})">
            <summary>
            构造函数
            </summary>
            <param name="options">数据库上下文选项</param>
        </member>
        <member name="P:LicenseManager.Data.LicenseDbContext.Licenses">
            <summary>
            许可证数据集
            </summary>
        </member>
        <member name="P:LicenseManager.Data.LicenseDbContext.AuthorizationTypes">
            <summary>
            授权类型数据集
            </summary>
        </member>
        <member name="P:LicenseManager.Data.LicenseDbContext.AppTemplates">
            <summary>
            应用程序模板数据集
            </summary>
        </member>
        <member name="P:LicenseManager.Data.LicenseDbContext.LicenseInfos">
            <summary>
            许可证信息数据集
            </summary>
        </member>
        <member name="P:LicenseManager.Data.LicenseDbContext.HardwareFingerprints">
            <summary>
            硬件指纹数据集
            </summary>
        </member>
        <member name="M:LicenseManager.Data.LicenseDbContext.OnModelCreating(Microsoft.EntityFrameworkCore.ModelBuilder)">
            <summary>
            配置数据模型
            </summary>
            <param name="modelBuilder">模型构建器</param>
        </member>
        <member name="M:LicenseManager.Data.LicenseDbContext.ConfigureLicense(Microsoft.EntityFrameworkCore.ModelBuilder)">
            <summary>
            配置License实体
            </summary>
        </member>
        <member name="M:LicenseManager.Data.LicenseDbContext.ConfigureAuthorizationType(Microsoft.EntityFrameworkCore.ModelBuilder)">
            <summary>
            配置AuthorizationType实体
            </summary>
        </member>
        <member name="M:LicenseManager.Data.LicenseDbContext.ConfigureAppTemplate(Microsoft.EntityFrameworkCore.ModelBuilder)">
            <summary>
            配置AppTemplate实体
            </summary>
        </member>
        <member name="M:LicenseManager.Data.LicenseDbContext.ConfigureLicenseInfo(Microsoft.EntityFrameworkCore.ModelBuilder)">
            <summary>
            配置LicenseInfo实体
            </summary>
        </member>
        <member name="M:LicenseManager.Data.LicenseDbContext.ConfigureHardwareFingerprint(Microsoft.EntityFrameworkCore.ModelBuilder)">
            <summary>
            配置HardwareFingerprint实体
            </summary>
        </member>
        <member name="M:LicenseManager.Data.LicenseDbContext.SeedData(Microsoft.EntityFrameworkCore.ModelBuilder)">
            <summary>
            添加种子数据
            </summary>
        </member>
        <member name="M:LicenseManager.Data.LicenseDbContext.SaveChanges">
            <summary>
            保存更改时自动更新时间戳
            </summary>
        </member>
        <member name="M:LicenseManager.Data.LicenseDbContext.SaveChangesAsync(System.Threading.CancellationToken)">
            <summary>
            异步保存更改时自动更新时间戳
            </summary>
        </member>
        <member name="M:LicenseManager.Data.LicenseDbContext.UpdateTimestamps">
            <summary>
            更新实体的时间戳
            </summary>
        </member>
        <member name="T:LicenseManager.Data.Repositories.ILicenseRepository">
            <summary>
            许可证仓储接口
            提供许可证相关的专门查询和操作方法
            </summary>
        </member>
        <member name="M:LicenseManager.Data.Repositories.ILicenseRepository.GetByLicenseKeyAsync(System.String)">
            <summary>
            根据许可证密钥获取许可证
            </summary>
            <param name="licenseKey">许可证密钥</param>
            <returns>许可证对象</returns>
        </member>
        <member name="M:LicenseManager.Data.Repositories.ILicenseRepository.GetByAppIdAsync(System.String)">
            <summary>
            根据应用程序ID获取许可证列表
            </summary>
            <param name="appId">应用程序ID</param>
            <returns>许可证列表</returns>
        </member>
        <member name="M:LicenseManager.Data.Repositories.ILicenseRepository.GetByCustomerEmailAsync(System.String)">
            <summary>
            根据客户邮箱获取许可证列表
            </summary>
            <param name="customerEmail">客户邮箱</param>
            <returns>许可证列表</returns>
        </member>
        <member name="M:LicenseManager.Data.Repositories.ILicenseRepository.GetExpiringLicensesAsync(System.Int32)">
            <summary>
            获取即将过期的许可证列表
            </summary>
            <param name="days">提前天数</param>
            <returns>即将过期的许可证列表</returns>
        </member>
        <member name="M:LicenseManager.Data.Repositories.ILicenseRepository.GetExpiredLicensesAsync">
            <summary>
            获取已过期的许可证列表
            </summary>
            <returns>已过期的许可证列表</returns>
        </member>
        <member name="M:LicenseManager.Data.Repositories.ILicenseRepository.GetActiveLicensesAsync">
            <summary>
            获取活跃的许可证列表
            </summary>
            <returns>活跃的许可证列表</returns>
        </member>
        <member name="M:LicenseManager.Data.Repositories.ILicenseRepository.GetByAuthorizationTypeAsync(System.Int32)">
            <summary>
            根据授权类型获取许可证列表
            </summary>
            <param name="authorizationTypeId">授权类型ID</param>
            <returns>许可证列表</returns>
        </member>
        <member name="M:LicenseManager.Data.Repositories.ILicenseRepository.GetByHardwareFingerprintAsync(System.String)">
            <summary>
            根据硬件指纹获取许可证
            </summary>
            <param name="hardwareFingerprint">硬件指纹</param>
            <returns>许可证对象</returns>
        </member>
        <member name="M:LicenseManager.Data.Repositories.ILicenseRepository.GetStatisticsAsync">
            <summary>
            获取许可证统计信息
            </summary>
            <returns>许可证统计信息</returns>
        </member>
        <member name="M:LicenseManager.Data.Repositories.ILicenseRepository.SearchLicensesAsync(System.String,System.Int32,System.Int32)">
            <summary>
            搜索许可证
            </summary>
            <param name="searchTerm">搜索关键词</param>
            <param name="pageIndex">页索引</param>
            <param name="pageSize">页大小</param>
            <returns>搜索结果</returns>
        </member>
        <member name="M:LicenseManager.Data.Repositories.ILicenseRepository.GetLicenseWithDetailsAsync(System.Int32)">
            <summary>
            获取许可证详细信息（包含关联数据）
            </summary>
            <param name="id">许可证ID</param>
            <returns>许可证详细信息</returns>
        </member>
        <member name="M:LicenseManager.Data.Repositories.ILicenseRepository.UpdateLicenseStatusAsync(System.Collections.Generic.IEnumerable{System.Int32},System.Boolean)">
            <summary>
            批量更新许可证状态
            </summary>
            <param name="licenseIds">许可证ID列表</param>
            <param name="isActive">新的激活状态</param>
            <returns>更新的许可证数量</returns>
        </member>
        <member name="M:LicenseManager.Data.Repositories.ILicenseRepository.ExtendLicenseAsync(System.Int32,System.Int32)">
            <summary>
            延长许可证有效期
            </summary>
            <param name="licenseId">许可证ID</param>
            <param name="additionalDays">延长天数</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:LicenseManager.Data.Repositories.ILicenseRepository.ValidateLicenseAsync(System.String,System.String)">
            <summary>
            验证许可证是否有效
            </summary>
            <param name="licenseKey">许可证密钥</param>
            <param name="hardwareFingerprint">硬件指纹（可选）</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:LicenseManager.Data.Repositories.ILicenseRepository.GetLicenseUsageHistoryAsync(System.Int32)">
            <summary>
            获取许可证使用历史
            </summary>
            <param name="licenseId">许可证ID</param>
            <returns>使用历史记录</returns>
        </member>
        <member name="T:LicenseManager.Data.Repositories.LicenseStatistics">
            <summary>
            许可证统计信息
            </summary>
        </member>
        <member name="P:LicenseManager.Data.Repositories.LicenseStatistics.TotalLicenses">
            <summary>
            总许可证数量
            </summary>
        </member>
        <member name="P:LicenseManager.Data.Repositories.LicenseStatistics.ActiveLicenses">
            <summary>
            活跃许可证数量
            </summary>
        </member>
        <member name="P:LicenseManager.Data.Repositories.LicenseStatistics.ExpiredLicenses">
            <summary>
            已过期许可证数量
            </summary>
        </member>
        <member name="P:LicenseManager.Data.Repositories.LicenseStatistics.ExpiringLicenses">
            <summary>
            即将过期许可证数量（30天内）
            </summary>
        </member>
        <member name="P:LicenseManager.Data.Repositories.LicenseStatistics.TrialLicenses">
            <summary>
            试用版许可证数量
            </summary>
        </member>
        <member name="P:LicenseManager.Data.Repositories.LicenseStatistics.NewLicensesThisMonth">
            <summary>
            本月新增许可证数量
            </summary>
        </member>
        <member name="P:LicenseManager.Data.Repositories.LicenseStatistics.LicensesByType">
            <summary>
            按授权类型分组的统计
            </summary>
        </member>
        <member name="P:LicenseManager.Data.Repositories.LicenseStatistics.LicensesByApp">
            <summary>
            按应用程序分组的统计
            </summary>
        </member>
        <member name="T:LicenseManager.Data.Repositories.LicenseValidationResult">
            <summary>
            许可证验证结果
            </summary>
        </member>
        <member name="P:LicenseManager.Data.Repositories.LicenseValidationResult.IsValid">
            <summary>
            是否有效
            </summary>
        </member>
        <member name="P:LicenseManager.Data.Repositories.LicenseValidationResult.License">
            <summary>
            许可证对象
            </summary>
        </member>
        <member name="P:LicenseManager.Data.Repositories.LicenseValidationResult.Message">
            <summary>
            验证消息
            </summary>
        </member>
        <member name="P:LicenseManager.Data.Repositories.LicenseValidationResult.ErrorCode">
            <summary>
            错误代码
            </summary>
        </member>
        <member name="P:LicenseManager.Data.Repositories.LicenseValidationResult.RemainingDays">
            <summary>
            剩余天数
            </summary>
        </member>
        <member name="P:LicenseManager.Data.Repositories.LicenseValidationResult.HardwareFingerprintMatch">
            <summary>
            硬件指纹匹配度
            </summary>
        </member>
        <member name="T:LicenseManager.Data.Repositories.LicenseUsageHistory">
            <summary>
            许可证使用历史
            </summary>
        </member>
        <member name="P:LicenseManager.Data.Repositories.LicenseUsageHistory.Id">
            <summary>
            历史记录ID
            </summary>
        </member>
        <member name="P:LicenseManager.Data.Repositories.LicenseUsageHistory.LicenseId">
            <summary>
            许可证ID
            </summary>
        </member>
        <member name="P:LicenseManager.Data.Repositories.LicenseUsageHistory.OperationType">
            <summary>
            操作类型
            </summary>
        </member>
        <member name="P:LicenseManager.Data.Repositories.LicenseUsageHistory.Description">
            <summary>
            操作描述
            </summary>
        </member>
        <member name="P:LicenseManager.Data.Repositories.LicenseUsageHistory.OperationTime">
            <summary>
            操作时间
            </summary>
        </member>
        <member name="P:LicenseManager.Data.Repositories.LicenseUsageHistory.Operator">
            <summary>
            操作者
            </summary>
        </member>
        <member name="P:LicenseManager.Data.Repositories.LicenseUsageHistory.ClientInfo">
            <summary>
            客户端信息
            </summary>
        </member>
        <member name="P:LicenseManager.Data.Repositories.LicenseUsageHistory.IpAddress">
            <summary>
            IP地址
            </summary>
        </member>
        <member name="T:LicenseManager.Data.Repositories.IRepository`1">
            <summary>
            通用仓储接口
            定义基本的CRUD操作和查询方法
            </summary>
            <typeparam name="T">实体类型</typeparam>
        </member>
        <member name="M:LicenseManager.Data.Repositories.IRepository`1.GetByIdAsync(System.Int32)">
            <summary>
            根据ID获取实体
            </summary>
            <param name="id">实体ID</param>
            <returns>实体对象</returns>
        </member>
        <member name="M:LicenseManager.Data.Repositories.IRepository`1.GetAllAsync">
            <summary>
            获取所有实体
            </summary>
            <returns>实体列表</returns>
        </member>
        <member name="M:LicenseManager.Data.Repositories.IRepository`1.FindAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            根据条件查找实体
            </summary>
            <param name="predicate">查询条件</param>
            <returns>符合条件的实体列表</returns>
        </member>
        <member name="M:LicenseManager.Data.Repositories.IRepository`1.FirstOrDefaultAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            根据条件查找单个实体
            </summary>
            <param name="predicate">查询条件</param>
            <returns>符合条件的第一个实体</returns>
        </member>
        <member name="M:LicenseManager.Data.Repositories.IRepository`1.GetPagedAsync``1(System.Int32,System.Int32,System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Linq.Expressions.Expression{System.Func{`0,``0}},System.Boolean)">
            <summary>
            分页查询
            </summary>
            <param name="pageIndex">页索引（从0开始）</param>
            <param name="pageSize">页大小</param>
            <param name="predicate">查询条件</param>
            <param name="orderBy">排序表达式</param>
            <param name="ascending">是否升序</param>
            <returns>分页结果</returns>
        </member>
        <member name="M:LicenseManager.Data.Repositories.IRepository`1.ExistsAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            检查实体是否存在
            </summary>
            <param name="predicate">查询条件</param>
            <returns>是否存在</returns>
        </member>
        <member name="M:LicenseManager.Data.Repositories.IRepository`1.CountAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            获取符合条件的实体数量
            </summary>
            <param name="predicate">查询条件</param>
            <returns>实体数量</returns>
        </member>
        <member name="M:LicenseManager.Data.Repositories.IRepository`1.AddAsync(`0)">
            <summary>
            添加实体
            </summary>
            <param name="entity">要添加的实体</param>
            <returns>添加后的实体</returns>
        </member>
        <member name="M:LicenseManager.Data.Repositories.IRepository`1.AddRangeAsync(System.Collections.Generic.IEnumerable{`0})">
            <summary>
            批量添加实体
            </summary>
            <param name="entities">要添加的实体列表</param>
            <returns>添加的实体数量</returns>
        </member>
        <member name="M:LicenseManager.Data.Repositories.IRepository`1.UpdateAsync(`0)">
            <summary>
            更新实体
            </summary>
            <param name="entity">要更新的实体</param>
            <returns>更新后的实体</returns>
        </member>
        <member name="M:LicenseManager.Data.Repositories.IRepository`1.UpdateRangeAsync(System.Collections.Generic.IEnumerable{`0})">
            <summary>
            批量更新实体
            </summary>
            <param name="entities">要更新的实体列表</param>
            <returns>更新的实体数量</returns>
        </member>
        <member name="M:LicenseManager.Data.Repositories.IRepository`1.DeleteAsync(`0)">
            <summary>
            删除实体
            </summary>
            <param name="entity">要删除的实体</param>
            <returns>是否删除成功</returns>
        </member>
        <member name="M:LicenseManager.Data.Repositories.IRepository`1.DeleteByIdAsync(System.Int32)">
            <summary>
            根据ID删除实体
            </summary>
            <param name="id">实体ID</param>
            <returns>是否删除成功</returns>
        </member>
        <member name="M:LicenseManager.Data.Repositories.IRepository`1.DeleteRangeAsync(System.Collections.Generic.IEnumerable{`0})">
            <summary>
            批量删除实体
            </summary>
            <param name="entities">要删除的实体列表</param>
            <returns>删除的实体数量</returns>
        </member>
        <member name="M:LicenseManager.Data.Repositories.IRepository`1.DeleteWhereAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            根据条件删除实体
            </summary>
            <param name="predicate">删除条件</param>
            <returns>删除的实体数量</returns>
        </member>
        <member name="M:LicenseManager.Data.Repositories.IRepository`1.SaveChangesAsync">
            <summary>
            保存更改
            </summary>
            <returns>受影响的行数</returns>
        </member>
        <member name="M:LicenseManager.Data.Repositories.IRepository`1.BeginTransactionAsync">
            <summary>
            开始事务
            </summary>
            <returns>事务对象</returns>
        </member>
        <member name="T:LicenseManager.Data.Repositories.PagedResult`1">
            <summary>
            分页结果
            </summary>
            <typeparam name="T">实体类型</typeparam>
        </member>
        <member name="P:LicenseManager.Data.Repositories.PagedResult`1.Items">
            <summary>
            数据列表
            </summary>
        </member>
        <member name="P:LicenseManager.Data.Repositories.PagedResult`1.TotalCount">
            <summary>
            总记录数
            </summary>
        </member>
        <member name="P:LicenseManager.Data.Repositories.PagedResult`1.PageIndex">
            <summary>
            页索引（从0开始）
            </summary>
        </member>
        <member name="P:LicenseManager.Data.Repositories.PagedResult`1.PageSize">
            <summary>
            页大小
            </summary>
        </member>
        <member name="P:LicenseManager.Data.Repositories.PagedResult`1.TotalPages">
            <summary>
            总页数
            </summary>
        </member>
        <member name="P:LicenseManager.Data.Repositories.PagedResult`1.HasPreviousPage">
            <summary>
            是否有上一页
            </summary>
        </member>
        <member name="P:LicenseManager.Data.Repositories.PagedResult`1.HasNextPage">
            <summary>
            是否有下一页
            </summary>
        </member>
        <member name="P:LicenseManager.Data.Repositories.PagedResult`1.StartIndex">
            <summary>
            当前页的起始记录索引（从1开始）
            </summary>
        </member>
        <member name="P:LicenseManager.Data.Repositories.PagedResult`1.EndIndex">
            <summary>
            当前页的结束记录索引
            </summary>
        </member>
        <member name="T:LicenseManager.Data.Repositories.IRepositoryTransaction">
            <summary>
            仓储事务接口
            </summary>
        </member>
        <member name="M:LicenseManager.Data.Repositories.IRepositoryTransaction.CommitAsync">
            <summary>
            提交事务
            </summary>
        </member>
        <member name="M:LicenseManager.Data.Repositories.IRepositoryTransaction.RollbackAsync">
            <summary>
            回滚事务
            </summary>
        </member>
        <member name="T:LicenseManager.Data.Repositories.Repository`1">
            <summary>
            通用仓储实现
            </summary>
            <typeparam name="T">实体类型</typeparam>
        </member>
        <member name="M:LicenseManager.Data.Repositories.Repository`1.#ctor(LicenseManager.Data.LicenseDbContext)">
            <summary>
            构造函数
            </summary>
            <param name="context">数据库上下文</param>
        </member>
        <member name="M:LicenseManager.Data.Repositories.Repository`1.GetByIdAsync(System.Int32)">
            <summary>
            根据ID获取实体
            </summary>
        </member>
        <member name="M:LicenseManager.Data.Repositories.Repository`1.GetAllAsync">
            <summary>
            获取所有实体
            </summary>
        </member>
        <member name="M:LicenseManager.Data.Repositories.Repository`1.FindAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            根据条件查找实体
            </summary>
        </member>
        <member name="M:LicenseManager.Data.Repositories.Repository`1.FirstOrDefaultAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            根据条件查找单个实体
            </summary>
        </member>
        <member name="M:LicenseManager.Data.Repositories.Repository`1.GetPagedAsync``1(System.Int32,System.Int32,System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Linq.Expressions.Expression{System.Func{`0,``0}},System.Boolean)">
            <summary>
            分页查询
            </summary>
        </member>
        <member name="M:LicenseManager.Data.Repositories.Repository`1.ExistsAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            检查实体是否存在
            </summary>
        </member>
        <member name="M:LicenseManager.Data.Repositories.Repository`1.CountAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            获取符合条件的实体数量
            </summary>
        </member>
        <member name="M:LicenseManager.Data.Repositories.Repository`1.AddAsync(`0)">
            <summary>
            添加实体
            </summary>
        </member>
        <member name="M:LicenseManager.Data.Repositories.Repository`1.AddRangeAsync(System.Collections.Generic.IEnumerable{`0})">
            <summary>
            批量添加实体
            </summary>
        </member>
        <member name="M:LicenseManager.Data.Repositories.Repository`1.UpdateAsync(`0)">
            <summary>
            更新实体
            </summary>
        </member>
        <member name="M:LicenseManager.Data.Repositories.Repository`1.UpdateRangeAsync(System.Collections.Generic.IEnumerable{`0})">
            <summary>
            批量更新实体
            </summary>
        </member>
        <member name="M:LicenseManager.Data.Repositories.Repository`1.DeleteAsync(`0)">
            <summary>
            删除实体
            </summary>
        </member>
        <member name="M:LicenseManager.Data.Repositories.Repository`1.DeleteByIdAsync(System.Int32)">
            <summary>
            根据ID删除实体
            </summary>
        </member>
        <member name="M:LicenseManager.Data.Repositories.Repository`1.DeleteRangeAsync(System.Collections.Generic.IEnumerable{`0})">
            <summary>
            批量删除实体
            </summary>
        </member>
        <member name="M:LicenseManager.Data.Repositories.Repository`1.DeleteWhereAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            根据条件删除实体
            </summary>
        </member>
        <member name="M:LicenseManager.Data.Repositories.Repository`1.SaveChangesAsync">
            <summary>
            保存更改
            </summary>
        </member>
        <member name="M:LicenseManager.Data.Repositories.Repository`1.BeginTransactionAsync">
            <summary>
            开始事务
            </summary>
        </member>
        <member name="T:LicenseManager.Data.Repositories.RepositoryTransaction">
            <summary>
            仓储事务实现
            </summary>
        </member>
        <member name="M:LicenseManager.Data.Repositories.RepositoryTransaction.#ctor(Microsoft.EntityFrameworkCore.Storage.IDbContextTransaction)">
            <summary>
            构造函数
            </summary>
            <param name="transaction">数据库事务</param>
        </member>
        <member name="M:LicenseManager.Data.Repositories.RepositoryTransaction.CommitAsync">
            <summary>
            提交事务
            </summary>
        </member>
        <member name="M:LicenseManager.Data.Repositories.RepositoryTransaction.RollbackAsync">
            <summary>
            回滚事务
            </summary>
        </member>
        <member name="M:LicenseManager.Data.Repositories.RepositoryTransaction.Dispose">
            <summary>
            释放资源
            </summary>
        </member>
        <member name="T:LicenseManager.MainWindow">
            <summary>
            Interaction logic for MainWindow.xaml
            </summary>
            <summary>
            MainWindow
            </summary>
        </member>
        <member name="M:LicenseManager.MainWindow.InitializeComponent">
            <summary>
            InitializeComponent
            </summary>
        </member>
        <member name="T:LicenseManager.Models.AppTemplate">
            <summary>
            应用程序模板实体模型
            存储不同应用程序的权限配置模板和相关信息
            </summary>
        </member>
        <member name="P:LicenseManager.Models.AppTemplate.Id">
            <summary>
            应用程序模板唯一标识符
            </summary>
        </member>
        <member name="P:LicenseManager.Models.AppTemplate.AppName">
            <summary>
            应用程序名称
            </summary>
        </member>
        <member name="P:LicenseManager.Models.AppTemplate.AppVersion">
            <summary>
            应用程序版本
            </summary>
        </member>
        <member name="P:LicenseManager.Models.AppTemplate.AppId">
            <summary>
            应用程序标识符（用于许可证生成）
            </summary>
        </member>
        <member name="P:LicenseManager.Models.AppTemplate.TemplateData">
            <summary>
            模板数据（JSON格式存储权限配置、功能列表等）
            </summary>
        </member>
        <member name="P:LicenseManager.Models.AppTemplate.Description">
            <summary>
            模板描述
            </summary>
        </member>
        <member name="P:LicenseManager.Models.AppTemplate.Developer">
            <summary>
            应用程序开发商
            </summary>
        </member>
        <member name="P:LicenseManager.Models.AppTemplate.Website">
            <summary>
            应用程序官网
            </summary>
        </member>
        <member name="P:LicenseManager.Models.AppTemplate.ContactEmail">
            <summary>
            联系邮箱
            </summary>
        </member>
        <member name="P:LicenseManager.Models.AppTemplate.AppType">
            <summary>
            应用程序类型
            </summary>
        </member>
        <member name="P:LicenseManager.Models.AppTemplate.SupportedOS">
            <summary>
            支持的操作系统
            </summary>
        </member>
        <member name="P:LicenseManager.Models.AppTemplate.MinimumRequirements">
            <summary>
            最低系统要求
            </summary>
        </member>
        <member name="P:LicenseManager.Models.AppTemplate.DefaultAuthorizationTypeId">
            <summary>
            默认授权类型ID
            </summary>
        </member>
        <member name="P:LicenseManager.Models.AppTemplate.EnableHardwareBinding">
            <summary>
            是否启用硬件绑定
            </summary>
        </member>
        <member name="P:LicenseManager.Models.AppTemplate.EnableNetworkValidation">
            <summary>
            是否启用网络验证
            </summary>
        </member>
        <member name="P:LicenseManager.Models.AppTemplate.LicenseFileExtension">
            <summary>
            许可证文件扩展名
            </summary>
        </member>
        <member name="P:LicenseManager.Models.AppTemplate.EncryptionType">
            <summary>
            加密算法类型
            </summary>
        </member>
        <member name="P:LicenseManager.Models.AppTemplate.PublicKey">
            <summary>
            公钥（用于许可证验证）
            </summary>
        </member>
        <member name="P:LicenseManager.Models.AppTemplate.PrivateKey">
            <summary>
            私钥（用于许可证生成，加密存储）
            </summary>
        </member>
        <member name="P:LicenseManager.Models.AppTemplate.TemplateVersion">
            <summary>
            模板版本号
            </summary>
        </member>
        <member name="P:LicenseManager.Models.AppTemplate.IsDefault">
            <summary>
            是否为默认模板
            </summary>
        </member>
        <member name="P:LicenseManager.Models.AppTemplate.IsEnabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:LicenseManager.Models.AppTemplate.SortOrder">
            <summary>
            排序顺序
            </summary>
        </member>
        <member name="P:LicenseManager.Models.AppTemplate.Tags">
            <summary>
            标签（用于分类和搜索）
            </summary>
        </member>
        <member name="P:LicenseManager.Models.AppTemplate.CreatedAt">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:LicenseManager.Models.AppTemplate.UpdatedAt">
            <summary>
            最后更新时间
            </summary>
        </member>
        <member name="P:LicenseManager.Models.AppTemplate.DefaultAuthorizationType">
            <summary>
            默认授权类型
            </summary>
        </member>
        <member name="P:LicenseManager.Models.AppTemplate.FullName">
            <summary>
            计算属性：应用程序完整名称
            </summary>
        </member>
        <member name="P:LicenseManager.Models.AppTemplate.TemplateDataSize">
            <summary>
            计算属性：模板数据大小（字节）
            </summary>
        </member>
        <member name="P:LicenseManager.Models.AppTemplate.HasEncryptionKeys">
            <summary>
            计算属性：是否配置了加密密钥
            </summary>
        </member>
        <member name="P:LicenseManager.Models.AppTemplate.StatusDescription">
            <summary>
            计算属性：状态描述
            </summary>
        </member>
        <member name="P:LicenseManager.Models.AppTemplate.TagList">
            <summary>
            计算属性：标签列表
            </summary>
        </member>
        <member name="T:LicenseManager.Models.AuthorizationType">
            <summary>
            授权类型实体模型
            定义不同的许可证授权类型（如试用版、标准版、专业版等）
            </summary>
        </member>
        <member name="P:LicenseManager.Models.AuthorizationType.Id">
            <summary>
            授权类型唯一标识符
            </summary>
        </member>
        <member name="P:LicenseManager.Models.AuthorizationType.Name">
            <summary>
            授权类型名称
            </summary>
        </member>
        <member name="P:LicenseManager.Models.AuthorizationType.Description">
            <summary>
            授权类型描述
            </summary>
        </member>
        <member name="P:LicenseManager.Models.AuthorizationType.MaxUsers">
            <summary>
            最大用户数量（-1表示无限制）
            </summary>
        </member>
        <member name="P:LicenseManager.Models.AuthorizationType.Features">
            <summary>
            功能特性列表（JSON格式存储）
            例如：["feature1", "feature2", "feature3"]
            </summary>
        </member>
        <member name="P:LicenseManager.Models.AuthorizationType.ValidityDays">
            <summary>
            默认有效期天数
            </summary>
        </member>
        <member name="P:LicenseManager.Models.AuthorizationType.AllowHardwareBinding">
            <summary>
            是否允许硬件绑定
            </summary>
        </member>
        <member name="P:LicenseManager.Models.AuthorizationType.AllowNetworkValidation">
            <summary>
            是否允许网络验证
            </summary>
        </member>
        <member name="P:LicenseManager.Models.AuthorizationType.MaxActivations">
            <summary>
            最大激活次数（-1表示无限制）
            </summary>
        </member>
        <member name="P:LicenseManager.Models.AuthorizationType.Priority">
            <summary>
            授权类型优先级（数字越大优先级越高）
            </summary>
        </member>
        <member name="P:LicenseManager.Models.AuthorizationType.IsTrial">
            <summary>
            是否为试用版
            </summary>
        </member>
        <member name="P:LicenseManager.Models.AuthorizationType.TrialDays">
            <summary>
            试用期天数（仅当IsTrial为true时有效）
            </summary>
        </member>
        <member name="P:LicenseManager.Models.AuthorizationType.Price">
            <summary>
            价格（用于显示，不参与业务逻辑）
            </summary>
        </member>
        <member name="P:LicenseManager.Models.AuthorizationType.Currency">
            <summary>
            货币单位
            </summary>
        </member>
        <member name="P:LicenseManager.Models.AuthorizationType.IsEnabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:LicenseManager.Models.AuthorizationType.SortOrder">
            <summary>
            排序顺序
            </summary>
        </member>
        <member name="P:LicenseManager.Models.AuthorizationType.CreatedAt">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:LicenseManager.Models.AuthorizationType.UpdatedAt">
            <summary>
            最后更新时间
            </summary>
        </member>
        <member name="P:LicenseManager.Models.AuthorizationType.Licenses">
            <summary>
            使用此授权类型的许可证列表
            </summary>
        </member>
        <member name="P:LicenseManager.Models.AuthorizationType.FeatureCount">
            <summary>
            计算属性：功能特性数量
            </summary>
        </member>
        <member name="P:LicenseManager.Models.AuthorizationType.DisplayName">
            <summary>
            计算属性：授权类型显示名称
            </summary>
        </member>
        <member name="P:LicenseManager.Models.AuthorizationType.PriceDisplay">
            <summary>
            计算属性：价格显示文本
            </summary>
        </member>
        <member name="T:LicenseManager.Models.HardwareFingerprint">
            <summary>
            硬件指纹实体模型
            存储用于硬件绑定的设备指纹信息
            </summary>
        </member>
        <member name="P:LicenseManager.Models.HardwareFingerprint.Id">
            <summary>
            硬件指纹唯一标识符
            </summary>
        </member>
        <member name="P:LicenseManager.Models.HardwareFingerprint.ProcessorId">
            <summary>
            处理器ID
            </summary>
        </member>
        <member name="P:LicenseManager.Models.HardwareFingerprint.MotherboardId">
            <summary>
            主板ID
            </summary>
        </member>
        <member name="P:LicenseManager.Models.HardwareFingerprint.DiskId">
            <summary>
            硬盘序列号
            </summary>
        </member>
        <member name="P:LicenseManager.Models.HardwareFingerprint.MacAddress">
            <summary>
            MAC地址
            </summary>
        </member>
        <member name="P:LicenseManager.Models.HardwareFingerprint.BiosSerialNumber">
            <summary>
            BIOS序列号
            </summary>
        </member>
        <member name="P:LicenseManager.Models.HardwareFingerprint.SystemUuid">
            <summary>
            系统UUID
            </summary>
        </member>
        <member name="P:LicenseManager.Models.HardwareFingerprint.MemoryInfo">
            <summary>
            内存信息
            </summary>
        </member>
        <member name="P:LicenseManager.Models.HardwareFingerprint.GraphicsInfo">
            <summary>
            显卡信息
            </summary>
        </member>
        <member name="P:LicenseManager.Models.HardwareFingerprint.OperatingSystem">
            <summary>
            操作系统信息
            </summary>
        </member>
        <member name="P:LicenseManager.Models.HardwareFingerprint.ComputerName">
            <summary>
            计算机名称
            </summary>
        </member>
        <member name="P:LicenseManager.Models.HardwareFingerprint.UserName">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:LicenseManager.Models.HardwareFingerprint.TimeZone">
            <summary>
            时区信息
            </summary>
        </member>
        <member name="P:LicenseManager.Models.HardwareFingerprint.FingerprintHash">
            <summary>
            综合指纹哈希值（基于多个硬件信息计算）
            </summary>
        </member>
        <member name="P:LicenseManager.Models.HardwareFingerprint.AlgorithmVersion">
            <summary>
            指纹生成算法版本
            </summary>
        </member>
        <member name="P:LicenseManager.Models.HardwareFingerprint.StrengthLevel">
            <summary>
            指纹强度等级（1-5，数字越大越严格）
            </summary>
        </member>
        <member name="P:LicenseManager.Models.HardwareFingerprint.AllowPartialMatch">
            <summary>
            是否允许部分匹配
            </summary>
        </member>
        <member name="P:LicenseManager.Models.HardwareFingerprint.PartialMatchThreshold">
            <summary>
            部分匹配阈值（0.0-1.0，表示匹配度）
            </summary>
        </member>
        <member name="P:LicenseManager.Models.HardwareFingerprint.Description">
            <summary>
            指纹描述
            </summary>
        </member>
        <member name="P:LicenseManager.Models.HardwareFingerprint.DeviceType">
            <summary>
            设备类型
            </summary>
        </member>
        <member name="P:LicenseManager.Models.HardwareFingerprint.IsVirtualMachine">
            <summary>
            是否为虚拟机
            </summary>
        </member>
        <member name="P:LicenseManager.Models.HardwareFingerprint.VirtualMachineType">
            <summary>
            虚拟机类型
            </summary>
        </member>
        <member name="P:LicenseManager.Models.HardwareFingerprint.LastValidatedAt">
            <summary>
            最后验证时间
            </summary>
        </member>
        <member name="P:LicenseManager.Models.HardwareFingerprint.ValidationCount">
            <summary>
            验证次数
            </summary>
        </member>
        <member name="P:LicenseManager.Models.HardwareFingerprint.IsEnabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:LicenseManager.Models.HardwareFingerprint.CreatedAt">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:LicenseManager.Models.HardwareFingerprint.UpdatedAt">
            <summary>
            最后更新时间
            </summary>
        </member>
        <member name="P:LicenseManager.Models.HardwareFingerprint.CompletenessScore">
            <summary>
            计算属性：硬件信息完整性
            </summary>
        </member>
        <member name="P:LicenseManager.Models.HardwareFingerprint.StatusDescription">
            <summary>
            计算属性：指纹状态描述
            </summary>
        </member>
        <member name="M:LicenseManager.Models.HardwareFingerprint.GenerateFingerprintHash">
            <summary>
            生成硬件指纹哈希值
            </summary>
            <returns>指纹哈希值</returns>
        </member>
        <member name="M:LicenseManager.Models.HardwareFingerprint.CalculateMatchScore(LicenseManager.Models.HardwareFingerprint)">
            <summary>
            验证指纹匹配度
            </summary>
            <param name="otherFingerprint">要比较的指纹</param>
            <returns>匹配度（0.0-1.0）</returns>
        </member>
        <member name="T:LicenseManager.Models.License">
            <summary>
            许可证实体模型
            表示系统中的一个软件许可证记录
            </summary>
        </member>
        <member name="P:LicenseManager.Models.License.Id">
            <summary>
            许可证唯一标识符
            </summary>
        </member>
        <member name="P:LicenseManager.Models.License.AppId">
            <summary>
            应用程序标识符
            </summary>
        </member>
        <member name="P:LicenseManager.Models.License.AppName">
            <summary>
            应用程序名称
            </summary>
        </member>
        <member name="P:LicenseManager.Models.License.AuthorizationTypeId">
            <summary>
            授权类型外键
            </summary>
        </member>
        <member name="P:LicenseManager.Models.License.LicenseKey">
            <summary>
            许可证密钥（加密后的许可证内容）
            </summary>
        </member>
        <member name="P:LicenseManager.Models.License.HardwareFingerprint">
            <summary>
            硬件指纹（用于硬件绑定）
            </summary>
        </member>
        <member name="P:LicenseManager.Models.License.IssueDate">
            <summary>
            许可证颁发日期
            </summary>
        </member>
        <member name="P:LicenseManager.Models.License.ExpiryDate">
            <summary>
            许可证过期日期
            </summary>
        </member>
        <member name="P:LicenseManager.Models.License.IsActive">
            <summary>
            许可证是否激活
            </summary>
        </member>
        <member name="P:LicenseManager.Models.License.CustomerName">
            <summary>
            客户名称
            </summary>
        </member>
        <member name="P:LicenseManager.Models.License.CustomerEmail">
            <summary>
            客户邮箱
            </summary>
        </member>
        <member name="P:LicenseManager.Models.License.MaxUsers">
            <summary>
            最大用户数量（-1表示无限制）
            </summary>
        </member>
        <member name="P:LicenseManager.Models.License.Features">
            <summary>
            许可证功能特性（JSON格式存储）
            </summary>
        </member>
        <member name="P:LicenseManager.Models.License.Notes">
            <summary>
            备注信息
            </summary>
        </member>
        <member name="P:LicenseManager.Models.License.CreatedAt">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:LicenseManager.Models.License.UpdatedAt">
            <summary>
            最后更新时间
            </summary>
        </member>
        <member name="P:LicenseManager.Models.License.AuthorizationType">
            <summary>
            关联的授权类型
            </summary>
        </member>
        <member name="P:LicenseManager.Models.License.LicenseInfos">
            <summary>
            关联的许可证详细信息
            </summary>
        </member>
        <member name="P:LicenseManager.Models.License.IsExpired">
            <summary>
            计算属性：许可证是否已过期
            </summary>
        </member>
        <member name="P:LicenseManager.Models.License.DaysRemaining">
            <summary>
            计算属性：许可证剩余天数
            </summary>
        </member>
        <member name="P:LicenseManager.Models.License.StatusDescription">
            <summary>
            计算属性：许可证状态描述
            </summary>
        </member>
        <member name="T:LicenseManager.Models.LicenseInfo">
            <summary>
            许可证信息实体模型
            存储许可证的扩展属性和自定义信息
            </summary>
        </member>
        <member name="P:LicenseManager.Models.LicenseInfo.Id">
            <summary>
            许可证信息唯一标识符
            </summary>
        </member>
        <member name="P:LicenseManager.Models.LicenseInfo.LicenseId">
            <summary>
            关联的许可证ID
            </summary>
        </member>
        <member name="P:LicenseManager.Models.LicenseInfo.PropertyName">
            <summary>
            属性名称
            </summary>
        </member>
        <member name="P:LicenseManager.Models.LicenseInfo.PropertyValue">
            <summary>
            属性值
            </summary>
        </member>
        <member name="P:LicenseManager.Models.LicenseInfo.PropertyType">
            <summary>
            属性类型（用于类型转换和验证）
            </summary>
        </member>
        <member name="P:LicenseManager.Models.LicenseInfo.Description">
            <summary>
            属性描述
            </summary>
        </member>
        <member name="P:LicenseManager.Models.LicenseInfo.PropertyGroup">
            <summary>
            属性分组（用于界面显示分组）
            </summary>
        </member>
        <member name="P:LicenseManager.Models.LicenseInfo.IsSensitive">
            <summary>
            是否为敏感信息（敏感信息在界面上会被遮蔽）
            </summary>
        </member>
        <member name="P:LicenseManager.Models.LicenseInfo.IsReadOnly">
            <summary>
            是否为只读属性
            </summary>
        </member>
        <member name="P:LicenseManager.Models.LicenseInfo.IsRequired">
            <summary>
            是否为必需属性
            </summary>
        </member>
        <member name="P:LicenseManager.Models.LicenseInfo.ValidationRule">
            <summary>
            属性验证规则（正则表达式或其他验证规则）
            </summary>
        </member>
        <member name="P:LicenseManager.Models.LicenseInfo.DefaultValue">
            <summary>
            默认值
            </summary>
        </member>
        <member name="P:LicenseManager.Models.LicenseInfo.SortOrder">
            <summary>
            排序顺序
            </summary>
        </member>
        <member name="P:LicenseManager.Models.LicenseInfo.IncludeInLicense">
            <summary>
            是否在许可证文件中包含此属性
            </summary>
        </member>
        <member name="P:LicenseManager.Models.LicenseInfo.ValidateOnCheck">
            <summary>
            是否在许可证验证时检查此属性
            </summary>
        </member>
        <member name="P:LicenseManager.Models.LicenseInfo.DisplayLabel">
            <summary>
            属性标签（用于界面显示）
            </summary>
        </member>
        <member name="P:LicenseManager.Models.LicenseInfo.HelpText">
            <summary>
            属性提示信息
            </summary>
        </member>
        <member name="P:LicenseManager.Models.LicenseInfo.CreatedAt">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:LicenseManager.Models.LicenseInfo.License">
            <summary>
            关联的许可证
            </summary>
        </member>
        <member name="P:LicenseManager.Models.LicenseInfo.Label">
            <summary>
            计算属性：显示标签（如果没有设置DisplayLabel则使用PropertyName）
            </summary>
        </member>
        <member name="P:LicenseManager.Models.LicenseInfo.DisplayValue">
            <summary>
            计算属性：显示值（敏感信息会被遮蔽）
            </summary>
        </member>
        <member name="P:LicenseManager.Models.LicenseInfo.PropertyTypeEnum">
            <summary>
            计算属性：属性类型枚举
            </summary>
        </member>
        <member name="M:LicenseManager.Models.LicenseInfo.GetTypedValue``1">
            <summary>
            获取强类型的属性值
            </summary>
            <typeparam name="T">目标类型</typeparam>
            <returns>转换后的值</returns>
        </member>
        <member name="M:LicenseManager.Models.LicenseInfo.ValidateValue">
            <summary>
            验证属性值是否符合验证规则
            </summary>
            <returns>验证结果</returns>
        </member>
        <member name="T:LicenseManager.Models.PropertyTypeEnum">
            <summary>
            属性类型枚举
            </summary>
        </member>
        <member name="T:LicenseManager.Services.IDataService">
            <summary>
            数据服务接口
            提供统一的数据访问和管理功能
            </summary>
        </member>
        <member name="P:LicenseManager.Services.IDataService.Licenses">
            <summary>
            许可证仓储
            </summary>
        </member>
        <member name="P:LicenseManager.Services.IDataService.AuthorizationTypes">
            <summary>
            授权类型仓储
            </summary>
        </member>
        <member name="P:LicenseManager.Services.IDataService.AppTemplates">
            <summary>
            应用程序模板仓储
            </summary>
        </member>
        <member name="P:LicenseManager.Services.IDataService.LicenseInfos">
            <summary>
            许可证信息仓储
            </summary>
        </member>
        <member name="P:LicenseManager.Services.IDataService.HardwareFingerprints">
            <summary>
            硬件指纹仓储
            </summary>
        </member>
        <member name="M:LicenseManager.Services.IDataService.InitializeDatabaseAsync(System.Boolean)">
            <summary>
            初始化数据库
            </summary>
            <param name="recreateDatabase">是否重新创建数据库</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:LicenseManager.Services.IDataService.CheckDatabaseConnectionAsync">
            <summary>
            检查数据库连接
            </summary>
            <returns>连接状态</returns>
        </member>
        <member name="M:LicenseManager.Services.IDataService.BackupDatabaseAsync(System.String)">
            <summary>
            备份数据库
            </summary>
            <param name="backupPath">备份路径</param>
            <returns>备份结果</returns>
        </member>
        <member name="M:LicenseManager.Services.IDataService.RestoreDatabaseAsync(System.String)">
            <summary>
            恢复数据库
            </summary>
            <param name="backupPath">备份文件路径</param>
            <returns>恢复结果</returns>
        </member>
        <member name="M:LicenseManager.Services.IDataService.GetDatabaseStatisticsAsync">
            <summary>
            获取数据库统计信息
            </summary>
            <returns>统计信息</returns>
        </member>
        <member name="M:LicenseManager.Services.IDataService.CleanupExpiredDataAsync(LicenseManager.Services.DataCleanupOptions)">
            <summary>
            清理过期数据
            </summary>
            <param name="options">清理选项</param>
            <returns>清理结果</returns>
        </member>
        <member name="M:LicenseManager.Services.IDataService.OptimizeDatabaseAsync">
            <summary>
            优化数据库
            </summary>
            <returns>优化结果</returns>
        </member>
        <member name="M:LicenseManager.Services.IDataService.ExportDataAsync(LicenseManager.Services.DataExportOptions)">
            <summary>
            导出数据
            </summary>
            <param name="exportOptions">导出选项</param>
            <returns>导出结果</returns>
        </member>
        <member name="M:LicenseManager.Services.IDataService.ImportDataAsync(LicenseManager.Services.DataImportOptions)">
            <summary>
            导入数据
            </summary>
            <param name="importOptions">导入选项</param>
            <returns>导入结果</returns>
        </member>
        <member name="M:LicenseManager.Services.IDataService.MigrateDataAsync(System.String)">
            <summary>
            执行数据迁移
            </summary>
            <param name="targetVersion">目标版本</param>
            <returns>迁移结果</returns>
        </member>
        <member name="M:LicenseManager.Services.IDataService.ValidateDataIntegrityAsync">
            <summary>
            验证数据完整性
            </summary>
            <returns>验证结果</returns>
        </member>
        <member name="M:LicenseManager.Services.IDataService.GetDatabaseSchemaAsync">
            <summary>
            获取数据库架构信息
            </summary>
            <returns>架构信息</returns>
        </member>
        <member name="M:LicenseManager.Services.IDataService.ExecuteRawSqlAsync(System.String,System.Object[])">
            <summary>
            执行原始SQL查询
            </summary>
            <param name="sql">SQL语句</param>
            <param name="parameters">参数</param>
            <returns>查询结果</returns>
        </member>
        <member name="M:LicenseManager.Services.IDataService.BeginTransactionAsync">
            <summary>
            开始事务
            </summary>
            <returns>事务对象</returns>
        </member>
        <member name="M:LicenseManager.Services.IDataService.SaveChangesAsync">
            <summary>
            保存所有更改
            </summary>
            <returns>受影响的行数</returns>
        </member>
        <member name="T:LicenseManager.Services.DatabaseConnectionStatus">
            <summary>
            数据库连接状态
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DatabaseConnectionStatus.IsConnected">
            <summary>
            是否连接成功
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DatabaseConnectionStatus.ConnectionString">
            <summary>
            连接字符串
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DatabaseConnectionStatus.DatabaseVersion">
            <summary>
            数据库版本
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DatabaseConnectionStatus.ConnectionTimeMs">
            <summary>
            连接时间（毫秒）
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DatabaseConnectionStatus.ErrorMessage">
            <summary>
            错误消息
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DatabaseConnectionStatus.CheckedAt">
            <summary>
            检查时间
            </summary>
        </member>
        <member name="T:LicenseManager.Services.DatabaseBackupResult">
            <summary>
            数据库备份结果
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DatabaseBackupResult.Success">
            <summary>
            是否成功
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DatabaseBackupResult.BackupFilePath">
            <summary>
            备份文件路径
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DatabaseBackupResult.BackupFileSize">
            <summary>
            备份文件大小（字节）
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DatabaseBackupResult.ElapsedMilliseconds">
            <summary>
            备份耗时（毫秒）
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DatabaseBackupResult.ErrorMessage">
            <summary>
            错误消息
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DatabaseBackupResult.BackupTime">
            <summary>
            备份时间
            </summary>
        </member>
        <member name="T:LicenseManager.Services.DatabaseRestoreResult">
            <summary>
            数据库恢复结果
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DatabaseRestoreResult.Success">
            <summary>
            是否成功
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DatabaseRestoreResult.RestoredRecords">
            <summary>
            恢复的记录数
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DatabaseRestoreResult.ElapsedMilliseconds">
            <summary>
            恢复耗时（毫秒）
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DatabaseRestoreResult.ErrorMessage">
            <summary>
            错误消息
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DatabaseRestoreResult.Warnings">
            <summary>
            警告消息
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DatabaseRestoreResult.RestoreTime">
            <summary>
            恢复时间
            </summary>
        </member>
        <member name="T:LicenseManager.Services.DatabaseStatistics">
            <summary>
            数据库统计信息
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DatabaseStatistics.DatabaseSize">
            <summary>
            数据库大小（字节）
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DatabaseStatistics.TableStatistics">
            <summary>
            表统计信息
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DatabaseStatistics.IndexStatistics">
            <summary>
            索引统计信息
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DatabaseStatistics.LastUpdated">
            <summary>
            最后更新时间
            </summary>
        </member>
        <member name="T:LicenseManager.Services.TableStatistics">
            <summary>
            表统计信息
            </summary>
        </member>
        <member name="P:LicenseManager.Services.TableStatistics.TableName">
            <summary>
            表名
            </summary>
        </member>
        <member name="P:LicenseManager.Services.TableStatistics.RecordCount">
            <summary>
            记录数
            </summary>
        </member>
        <member name="P:LicenseManager.Services.TableStatistics.TableSize">
            <summary>
            表大小（字节）
            </summary>
        </member>
        <member name="P:LicenseManager.Services.TableStatistics.LastUpdated">
            <summary>
            最后更新时间
            </summary>
        </member>
        <member name="T:LicenseManager.Services.IndexStatistics">
            <summary>
            索引统计信息
            </summary>
        </member>
        <member name="P:LicenseManager.Services.IndexStatistics.IndexName">
            <summary>
            索引名
            </summary>
        </member>
        <member name="P:LicenseManager.Services.IndexStatistics.TableName">
            <summary>
            表名
            </summary>
        </member>
        <member name="P:LicenseManager.Services.IndexStatistics.IndexSize">
            <summary>
            索引大小（字节）
            </summary>
        </member>
        <member name="P:LicenseManager.Services.IndexStatistics.UsageCount">
            <summary>
            使用次数
            </summary>
        </member>
        <member name="T:LicenseManager.Services.DataCleanupOptions">
            <summary>
            数据清理选项
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DataCleanupOptions.CleanupExpiredLicenses">
            <summary>
            清理过期许可证
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DataCleanupOptions.ExpiredDaysThreshold">
            <summary>
            过期天数阈值
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DataCleanupOptions.CleanupLogs">
            <summary>
            清理日志记录
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DataCleanupOptions.LogRetentionDays">
            <summary>
            日志保留天数
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DataCleanupOptions.CleanupTempFiles">
            <summary>
            清理临时文件
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DataCleanupOptions.CompactDatabase">
            <summary>
            压缩数据库
            </summary>
        </member>
        <member name="T:LicenseManager.Services.DataCleanupResult">
            <summary>
            数据清理结果
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DataCleanupResult.Success">
            <summary>
            是否成功
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DataCleanupResult.CleanedRecords">
            <summary>
            清理的记录数
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DataCleanupResult.FreedSpace">
            <summary>
            释放的空间（字节）
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DataCleanupResult.ElapsedMilliseconds">
            <summary>
            清理耗时（毫秒）
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DataCleanupResult.CleanupDetails">
            <summary>
            清理详情
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DataCleanupResult.ErrorMessage">
            <summary>
            错误消息
            </summary>
        </member>
        <member name="T:LicenseManager.Services.DatabaseOptimizationResult">
            <summary>
            数据库优化结果
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DatabaseOptimizationResult.Success">
            <summary>
            是否成功
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DatabaseOptimizationResult.SizeBefore">
            <summary>
            优化前大小（字节）
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DatabaseOptimizationResult.SizeAfter">
            <summary>
            优化后大小（字节）
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DatabaseOptimizationResult.SpaceSaved">
            <summary>
            节省的空间（字节）
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DatabaseOptimizationResult.ElapsedMilliseconds">
            <summary>
            优化耗时（毫秒）
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DatabaseOptimizationResult.OptimizationOperations">
            <summary>
            优化操作列表
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DatabaseOptimizationResult.ErrorMessage">
            <summary>
            错误消息
            </summary>
        </member>
        <member name="T:LicenseManager.Services.DataExportOptions">
            <summary>
            数据导出选项
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DataExportOptions.Format">
            <summary>
            导出格式
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DataExportOptions.ExportPath">
            <summary>
            导出路径
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DataExportOptions.Tables">
            <summary>
            要导出的表
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DataExportOptions.IncludeSchema">
            <summary>
            是否包含架构
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DataExportOptions.IncludeData">
            <summary>
            是否包含数据
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DataExportOptions.Compress">
            <summary>
            是否压缩
            </summary>
        </member>
        <member name="T:LicenseManager.Services.DataExportResult">
            <summary>
            数据导出结果
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DataExportResult.Success">
            <summary>
            是否成功
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DataExportResult.ExportFilePath">
            <summary>
            导出文件路径
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DataExportResult.ExportedRecords">
            <summary>
            导出的记录数
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DataExportResult.FileSize">
            <summary>
            文件大小（字节）
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DataExportResult.ElapsedMilliseconds">
            <summary>
            导出耗时（毫秒）
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DataExportResult.ErrorMessage">
            <summary>
            错误消息
            </summary>
        </member>
        <member name="T:LicenseManager.Services.DataImportOptions">
            <summary>
            数据导入选项
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DataImportOptions.ImportFilePath">
            <summary>
            导入文件路径
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DataImportOptions.Format">
            <summary>
            导入格式
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DataImportOptions.OverwriteExisting">
            <summary>
            是否覆盖现有数据
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DataImportOptions.ValidateData">
            <summary>
            是否验证数据
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DataImportOptions.BatchSize">
            <summary>
            批量大小
            </summary>
        </member>
        <member name="T:LicenseManager.Services.DataImportResult">
            <summary>
            数据导入结果
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DataImportResult.Success">
            <summary>
            是否成功
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DataImportResult.ImportedRecords">
            <summary>
            导入的记录数
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DataImportResult.SkippedRecords">
            <summary>
            跳过的记录数
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DataImportResult.ErrorRecords">
            <summary>
            错误记录数
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DataImportResult.ElapsedMilliseconds">
            <summary>
            导入耗时（毫秒）
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DataImportResult.ErrorMessage">
            <summary>
            错误消息
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DataImportResult.Warnings">
            <summary>
            警告消息
            </summary>
        </member>
        <member name="T:LicenseManager.Services.DataMigrationResult">
            <summary>
            数据迁移结果
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DataMigrationResult.Success">
            <summary>
            是否成功
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DataMigrationResult.SourceVersion">
            <summary>
            源版本
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DataMigrationResult.TargetVersion">
            <summary>
            目标版本
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DataMigrationResult.MigratedRecords">
            <summary>
            迁移的记录数
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DataMigrationResult.ElapsedMilliseconds">
            <summary>
            迁移耗时（毫秒）
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DataMigrationResult.MigrationSteps">
            <summary>
            迁移步骤
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DataMigrationResult.ErrorMessage">
            <summary>
            错误消息
            </summary>
        </member>
        <member name="T:LicenseManager.Services.DataIntegrityResult">
            <summary>
            数据完整性结果
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DataIntegrityResult.IsValid">
            <summary>
            是否通过验证
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DataIntegrityResult.ValidatedTables">
            <summary>
            验证的表数量
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DataIntegrityResult.IssuesFound">
            <summary>
            发现的问题数量
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DataIntegrityResult.ValidationDetails">
            <summary>
            验证详情
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DataIntegrityResult.ElapsedMilliseconds">
            <summary>
            验证耗时（毫秒）
            </summary>
        </member>
        <member name="T:LicenseManager.Services.ValidationIssue">
            <summary>
            验证问题
            </summary>
        </member>
        <member name="P:LicenseManager.Services.ValidationIssue.IssueType">
            <summary>
            问题类型
            </summary>
        </member>
        <member name="P:LicenseManager.Services.ValidationIssue.Description">
            <summary>
            问题描述
            </summary>
        </member>
        <member name="P:LicenseManager.Services.ValidationIssue.Severity">
            <summary>
            严重程度
            </summary>
        </member>
        <member name="P:LicenseManager.Services.ValidationIssue.SuggestedFix">
            <summary>
            建议修复方案
            </summary>
        </member>
        <member name="T:LicenseManager.Services.DatabaseSchemaInfo">
            <summary>
            数据库架构信息
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DatabaseSchemaInfo.DatabaseName">
            <summary>
            数据库名称
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DatabaseSchemaInfo.SchemaVersion">
            <summary>
            架构版本
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DatabaseSchemaInfo.Tables">
            <summary>
            表信息
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DatabaseSchemaInfo.Indexes">
            <summary>
            索引信息
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DatabaseSchemaInfo.ForeignKeys">
            <summary>
            外键信息
            </summary>
        </member>
        <member name="T:LicenseManager.Services.TableInfo">
            <summary>
            表信息
            </summary>
        </member>
        <member name="P:LicenseManager.Services.TableInfo.Name">
            <summary>
            表名
            </summary>
        </member>
        <member name="P:LicenseManager.Services.TableInfo.Columns">
            <summary>
            列信息
            </summary>
        </member>
        <member name="T:LicenseManager.Services.ColumnInfo">
            <summary>
            列信息
            </summary>
        </member>
        <member name="P:LicenseManager.Services.ColumnInfo.Name">
            <summary>
            列名
            </summary>
        </member>
        <member name="P:LicenseManager.Services.ColumnInfo.DataType">
            <summary>
            数据类型
            </summary>
        </member>
        <member name="P:LicenseManager.Services.ColumnInfo.IsNullable">
            <summary>
            是否可空
            </summary>
        </member>
        <member name="P:LicenseManager.Services.ColumnInfo.IsPrimaryKey">
            <summary>
            是否主键
            </summary>
        </member>
        <member name="P:LicenseManager.Services.ColumnInfo.DefaultValue">
            <summary>
            默认值
            </summary>
        </member>
        <member name="T:LicenseManager.Services.IndexInfo">
            <summary>
            索引信息
            </summary>
        </member>
        <member name="P:LicenseManager.Services.IndexInfo.Name">
            <summary>
            索引名
            </summary>
        </member>
        <member name="P:LicenseManager.Services.IndexInfo.TableName">
            <summary>
            表名
            </summary>
        </member>
        <member name="P:LicenseManager.Services.IndexInfo.Columns">
            <summary>
            列名列表
            </summary>
        </member>
        <member name="P:LicenseManager.Services.IndexInfo.IsUnique">
            <summary>
            是否唯一
            </summary>
        </member>
        <member name="T:LicenseManager.Services.ForeignKeyInfo">
            <summary>
            外键信息
            </summary>
        </member>
        <member name="P:LicenseManager.Services.ForeignKeyInfo.Name">
            <summary>
            外键名
            </summary>
        </member>
        <member name="P:LicenseManager.Services.ForeignKeyInfo.SourceTable">
            <summary>
            源表
            </summary>
        </member>
        <member name="P:LicenseManager.Services.ForeignKeyInfo.SourceColumn">
            <summary>
            源列
            </summary>
        </member>
        <member name="P:LicenseManager.Services.ForeignKeyInfo.TargetTable">
            <summary>
            目标表
            </summary>
        </member>
        <member name="P:LicenseManager.Services.ForeignKeyInfo.TargetColumn">
            <summary>
            目标列
            </summary>
        </member>
        <member name="T:LicenseManager.Services.QueryResult">
            <summary>
            查询结果
            </summary>
        </member>
        <member name="P:LicenseManager.Services.QueryResult.Success">
            <summary>
            是否成功
            </summary>
        </member>
        <member name="P:LicenseManager.Services.QueryResult.Data">
            <summary>
            结果数据
            </summary>
        </member>
        <member name="P:LicenseManager.Services.QueryResult.AffectedRows">
            <summary>
            受影响的行数
            </summary>
        </member>
        <member name="P:LicenseManager.Services.QueryResult.ElapsedMilliseconds">
            <summary>
            执行耗时（毫秒）
            </summary>
        </member>
        <member name="P:LicenseManager.Services.QueryResult.ErrorMessage">
            <summary>
            错误消息
            </summary>
        </member>
        <member name="T:LicenseManager.Services.DataExportFormat">
            <summary>
            数据导出格式
            </summary>
        </member>
        <member name="T:LicenseManager.Services.IssueSeverity">
            <summary>
            问题严重程度
            </summary>
        </member>
        <member name="T:LicenseManager.Services.IEncryptionService">
            <summary>
            加密服务接口
            提供RSA、AES等加密算法的统一接口
            </summary>
        </member>
        <member name="M:LicenseManager.Services.IEncryptionService.GenerateRsaKeyPair(System.Int32)">
            <summary>
            生成RSA密钥对
            </summary>
            <param name="keySize">密钥长度（位）</param>
            <returns>RSA密钥对</returns>
        </member>
        <member name="M:LicenseManager.Services.IEncryptionService.RsaEncrypt(System.Byte[],System.String)">
            <summary>
            RSA加密
            </summary>
            <param name="data">要加密的数据</param>
            <param name="publicKey">公钥</param>
            <returns>加密后的数据</returns>
        </member>
        <member name="M:LicenseManager.Services.IEncryptionService.RsaDecrypt(System.Byte[],System.String)">
            <summary>
            RSA解密
            </summary>
            <param name="encryptedData">加密的数据</param>
            <param name="privateKey">私钥</param>
            <returns>解密后的数据</returns>
        </member>
        <member name="M:LicenseManager.Services.IEncryptionService.RsaSign(System.Byte[],System.String)">
            <summary>
            RSA签名
            </summary>
            <param name="data">要签名的数据</param>
            <param name="privateKey">私钥</param>
            <returns>签名</returns>
        </member>
        <member name="M:LicenseManager.Services.IEncryptionService.RsaVerifySignature(System.Byte[],System.Byte[],System.String)">
            <summary>
            RSA验证签名
            </summary>
            <param name="data">原始数据</param>
            <param name="signature">签名</param>
            <param name="publicKey">公钥</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:LicenseManager.Services.IEncryptionService.GenerateAesKey(System.Int32)">
            <summary>
            生成AES密钥
            </summary>
            <param name="keySize">密钥长度（位）</param>
            <returns>AES密钥</returns>
        </member>
        <member name="M:LicenseManager.Services.IEncryptionService.AesEncrypt(System.Byte[],System.Byte[],System.Byte[])">
            <summary>
            AES加密
            </summary>
            <param name="data">要加密的数据</param>
            <param name="key">密钥</param>
            <param name="iv">初始化向量</param>
            <returns>加密后的数据</returns>
        </member>
        <member name="M:LicenseManager.Services.IEncryptionService.AesDecrypt(System.Byte[],System.Byte[],System.Byte[])">
            <summary>
            AES解密
            </summary>
            <param name="encryptedData">加密的数据</param>
            <param name="key">密钥</param>
            <param name="iv">初始化向量</param>
            <returns>解密后的数据</returns>
        </member>
        <member name="M:LicenseManager.Services.IEncryptionService.ComputeSha256Hash(System.Byte[])">
            <summary>
            计算SHA256哈希
            </summary>
            <param name="data">要计算哈希的数据</param>
            <returns>哈希值</returns>
        </member>
        <member name="M:LicenseManager.Services.IEncryptionService.ComputeMd5Hash(System.Byte[])">
            <summary>
            计算MD5哈希
            </summary>
            <param name="data">要计算哈希的数据</param>
            <returns>哈希值</returns>
        </member>
        <member name="M:LicenseManager.Services.IEncryptionService.GenerateRandomBytes(System.Int32)">
            <summary>
            生成随机字节
            </summary>
            <param name="length">字节长度</param>
            <returns>随机字节数组</returns>
        </member>
        <member name="M:LicenseManager.Services.IEncryptionService.GenerateRandomString(System.Int32,System.Boolean)">
            <summary>
            生成随机字符串
            </summary>
            <param name="length">字符串长度</param>
            <param name="includeSpecialChars">是否包含特殊字符</param>
            <returns>随机字符串</returns>
        </member>
        <member name="M:LicenseManager.Services.IEncryptionService.ToBase64String(System.Byte[])">
            <summary>
            Base64编码
            </summary>
            <param name="data">要编码的数据</param>
            <returns>Base64字符串</returns>
        </member>
        <member name="M:LicenseManager.Services.IEncryptionService.FromBase64String(System.String)">
            <summary>
            Base64解码
            </summary>
            <param name="base64String">Base64字符串</param>
            <returns>解码后的数据</returns>
        </member>
        <member name="M:LicenseManager.Services.IEncryptionService.ToHexString(System.Byte[])">
            <summary>
            十六进制编码
            </summary>
            <param name="data">要编码的数据</param>
            <returns>十六进制字符串</returns>
        </member>
        <member name="M:LicenseManager.Services.IEncryptionService.FromHexString(System.String)">
            <summary>
            十六进制解码
            </summary>
            <param name="hexString">十六进制字符串</param>
            <returns>解码后的数据</returns>
        </member>
        <member name="M:LicenseManager.Services.IEncryptionService.HashPassword(System.String,System.Byte[],System.Int32,System.Int32)">
            <summary>
            密码哈希（使用PBKDF2）
            </summary>
            <param name="password">密码</param>
            <param name="salt">盐值</param>
            <param name="iterations">迭代次数</param>
            <param name="hashLength">哈希长度</param>
            <returns>密码哈希</returns>
        </member>
        <member name="M:LicenseManager.Services.IEncryptionService.VerifyPassword(System.String,System.Byte[],System.Byte[],System.Int32)">
            <summary>
            验证密码
            </summary>
            <param name="password">密码</param>
            <param name="hash">存储的哈希</param>
            <param name="salt">盐值</param>
            <param name="iterations">迭代次数</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:LicenseManager.Services.IEncryptionService.EncryptLicenseData(LicenseManager.Services.LicenseData,System.String)">
            <summary>
            加密许可证数据
            </summary>
            <param name="licenseData">许可证数据</param>
            <param name="publicKey">公钥</param>
            <returns>加密结果</returns>
        </member>
        <member name="M:LicenseManager.Services.IEncryptionService.DecryptLicenseData(System.Byte[],System.String)">
            <summary>
            解密许可证数据
            </summary>
            <param name="encryptedData">加密的数据</param>
            <param name="privateKey">私钥</param>
            <returns>解密结果</returns>
        </member>
        <member name="M:LicenseManager.Services.IEncryptionService.VerifyLicenseIntegrity(LicenseManager.Services.LicenseData,System.Byte[],System.String)">
            <summary>
            验证许可证完整性
            </summary>
            <param name="licenseData">许可证数据</param>
            <param name="signature">签名</param>
            <param name="publicKey">公钥</param>
            <returns>验证结果</returns>
        </member>
        <member name="T:LicenseManager.Services.RsaKeyPair">
            <summary>
            RSA密钥对
            </summary>
        </member>
        <member name="P:LicenseManager.Services.RsaKeyPair.PublicKey">
            <summary>
            公钥
            </summary>
        </member>
        <member name="P:LicenseManager.Services.RsaKeyPair.PrivateKey">
            <summary>
            私钥
            </summary>
        </member>
        <member name="P:LicenseManager.Services.RsaKeyPair.KeySize">
            <summary>
            密钥长度
            </summary>
        </member>
        <member name="P:LicenseManager.Services.RsaKeyPair.GeneratedAt">
            <summary>
            生成时间
            </summary>
        </member>
        <member name="T:LicenseManager.Services.AesKey">
            <summary>
            AES密钥
            </summary>
        </member>
        <member name="P:LicenseManager.Services.AesKey.Key">
            <summary>
            密钥
            </summary>
        </member>
        <member name="P:LicenseManager.Services.AesKey.IV">
            <summary>
            初始化向量
            </summary>
        </member>
        <member name="P:LicenseManager.Services.AesKey.KeySize">
            <summary>
            密钥长度
            </summary>
        </member>
        <member name="P:LicenseManager.Services.AesKey.GeneratedAt">
            <summary>
            生成时间
            </summary>
        </member>
        <member name="T:LicenseManager.Services.EncryptionResult">
            <summary>
            加密结果
            </summary>
        </member>
        <member name="P:LicenseManager.Services.EncryptionResult.Success">
            <summary>
            是否成功
            </summary>
        </member>
        <member name="P:LicenseManager.Services.EncryptionResult.EncryptedData">
            <summary>
            加密后的数据
            </summary>
        </member>
        <member name="P:LicenseManager.Services.EncryptionResult.Signature">
            <summary>
            签名
            </summary>
        </member>
        <member name="P:LicenseManager.Services.EncryptionResult.ErrorMessage">
            <summary>
            错误消息
            </summary>
        </member>
        <member name="P:LicenseManager.Services.EncryptionResult.Algorithm">
            <summary>
            加密算法
            </summary>
        </member>
        <member name="P:LicenseManager.Services.EncryptionResult.EncryptedAt">
            <summary>
            加密时间
            </summary>
        </member>
        <member name="T:LicenseManager.Services.DecryptionResult`1">
            <summary>
            解密结果
            </summary>
            <typeparam name="T">解密后的数据类型</typeparam>
        </member>
        <member name="P:LicenseManager.Services.DecryptionResult`1.Success">
            <summary>
            是否成功
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DecryptionResult`1.Data">
            <summary>
            解密后的数据
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DecryptionResult`1.ErrorMessage">
            <summary>
            错误消息
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DecryptionResult`1.IntegrityVerified">
            <summary>
            是否验证了完整性
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DecryptionResult`1.DecryptedAt">
            <summary>
            解密时间
            </summary>
        </member>
        <member name="T:LicenseManager.Services.LicenseData">
            <summary>
            许可证数据（用于加密/解密）
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseData.AppId">
            <summary>
            应用程序ID
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseData.AppName">
            <summary>
            应用程序名称
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseData.LicenseId">
            <summary>
            许可证ID
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseData.AuthorizationType">
            <summary>
            授权类型
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseData.CustomerName">
            <summary>
            客户信息
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseData.CustomerEmail">
            <summary>
            客户邮箱
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseData.IssueDate">
            <summary>
            颁发日期
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseData.ExpiryDate">
            <summary>
            过期日期
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseData.MaxUsers">
            <summary>
            最大用户数
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseData.HardwareFingerprint">
            <summary>
            硬件指纹
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseData.Features">
            <summary>
            功能特性
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseData.CustomProperties">
            <summary>
            自定义属性
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseData.Version">
            <summary>
            版本号
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseData.Checksum">
            <summary>
            校验和
            </summary>
        </member>
        <member name="T:LicenseManager.Services.IHardwareFingerprintService">
            <summary>
            硬件指纹服务接口
            提供硬件信息收集、指纹生成、匹配验证等功能
            </summary>
        </member>
        <member name="M:LicenseManager.Services.IHardwareFingerprintService.GetCurrentHardwareFingerprintAsync(System.Int32)">
            <summary>
            获取当前系统的硬件指纹
            </summary>
            <param name="strengthLevel">指纹强度等级（1-5）</param>
            <returns>硬件指纹对象</returns>
        </member>
        <member name="M:LicenseManager.Services.IHardwareFingerprintService.GenerateFingerprintHash(LicenseManager.Models.HardwareFingerprint)">
            <summary>
            生成硬件指纹哈希值
            </summary>
            <param name="fingerprint">硬件指纹对象</param>
            <returns>指纹哈希值</returns>
        </member>
        <member name="M:LicenseManager.Services.IHardwareFingerprintService.CalculateMatchScore(LicenseManager.Models.HardwareFingerprint,LicenseManager.Models.HardwareFingerprint)">
            <summary>
            比较两个硬件指纹的匹配度
            </summary>
            <param name="fingerprint1">指纹1</param>
            <param name="fingerprint2">指纹2</param>
            <returns>匹配度（0.0-1.0）</returns>
        </member>
        <member name="M:LicenseManager.Services.IHardwareFingerprintService.ValidateFingerprint(LicenseManager.Models.HardwareFingerprint,LicenseManager.Models.HardwareFingerprint,System.Decimal)">
            <summary>
            验证硬件指纹是否匹配
            </summary>
            <param name="storedFingerprint">存储的指纹</param>
            <param name="currentFingerprint">当前指纹</param>
            <param name="threshold">匹配阈值</param>
            <returns>匹配结果</returns>
        </member>
        <member name="M:LicenseManager.Services.IHardwareFingerprintService.GetProcessorInfoAsync">
            <summary>
            获取处理器信息
            </summary>
            <returns>处理器信息</returns>
        </member>
        <member name="M:LicenseManager.Services.IHardwareFingerprintService.GetMotherboardInfoAsync">
            <summary>
            获取主板信息
            </summary>
            <returns>主板信息</returns>
        </member>
        <member name="M:LicenseManager.Services.IHardwareFingerprintService.GetDiskInfoAsync">
            <summary>
            获取硬盘信息
            </summary>
            <returns>硬盘信息列表</returns>
        </member>
        <member name="M:LicenseManager.Services.IHardwareFingerprintService.GetNetworkAdapterInfoAsync">
            <summary>
            获取网络适配器信息
            </summary>
            <returns>网络适配器信息列表</returns>
        </member>
        <member name="M:LicenseManager.Services.IHardwareFingerprintService.GetMemoryInfoAsync">
            <summary>
            获取内存信息
            </summary>
            <returns>内存信息</returns>
        </member>
        <member name="M:LicenseManager.Services.IHardwareFingerprintService.GetGraphicsInfoAsync">
            <summary>
            获取显卡信息
            </summary>
            <returns>显卡信息列表</returns>
        </member>
        <member name="M:LicenseManager.Services.IHardwareFingerprintService.GetBiosInfoAsync">
            <summary>
            获取BIOS信息
            </summary>
            <returns>BIOS信息</returns>
        </member>
        <member name="M:LicenseManager.Services.IHardwareFingerprintService.GetOperatingSystemInfoAsync">
            <summary>
            获取操作系统信息
            </summary>
            <returns>操作系统信息</returns>
        </member>
        <member name="M:LicenseManager.Services.IHardwareFingerprintService.DetectVirtualMachineAsync">
            <summary>
            检测是否为虚拟机
            </summary>
            <returns>虚拟机检测结果</returns>
        </member>
        <member name="M:LicenseManager.Services.IHardwareFingerprintService.GetSystemUuidAsync">
            <summary>
            获取系统唯一标识符
            </summary>
            <returns>系统UUID</returns>
        </member>
        <member name="M:LicenseManager.Services.IHardwareFingerprintService.GetHardwareChangeHistoryAsync(System.Int32)">
            <summary>
            获取硬件变更历史
            </summary>
            <param name="fingerprintId">指纹ID</param>
            <returns>变更历史</returns>
        </member>
        <member name="M:LicenseManager.Services.IHardwareFingerprintService.RecordHardwareChangeAsync(LicenseManager.Models.HardwareFingerprint,LicenseManager.Models.HardwareFingerprint)">
            <summary>
            记录硬件变更
            </summary>
            <param name="oldFingerprint">旧指纹</param>
            <param name="newFingerprint">新指纹</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:LicenseManager.Services.IHardwareFingerprintService.GenerateFingerprintReport(LicenseManager.Models.HardwareFingerprint)">
            <summary>
            生成硬件指纹报告
            </summary>
            <param name="fingerprint">硬件指纹</param>
            <returns>指纹报告</returns>
        </member>
        <member name="M:LicenseManager.Services.IHardwareFingerprintService.ExportFingerprintAsync(LicenseManager.Models.HardwareFingerprint,LicenseManager.Services.FingerprintExportFormat)">
            <summary>
            导出硬件指纹
            </summary>
            <param name="fingerprint">硬件指纹</param>
            <param name="format">导出格式</param>
            <returns>导出结果</returns>
        </member>
        <member name="M:LicenseManager.Services.IHardwareFingerprintService.ImportFingerprintAsync(System.Byte[],LicenseManager.Services.FingerprintExportFormat)">
            <summary>
            导入硬件指纹
            </summary>
            <param name="data">指纹数据</param>
            <param name="format">数据格式</param>
            <returns>导入结果</returns>
        </member>
        <member name="M:LicenseManager.Services.IHardwareFingerprintService.OptimizeFingerprint(LicenseManager.Models.HardwareFingerprint,LicenseManager.Services.FingerprintOptimizationOptions)">
            <summary>
            优化硬件指纹配置
            </summary>
            <param name="fingerprint">硬件指纹</param>
            <param name="optimizationOptions">优化选项</param>
            <returns>优化后的指纹</returns>
        </member>
        <member name="T:LicenseManager.Services.FingerprintMatchResult">
            <summary>
            指纹匹配结果
            </summary>
        </member>
        <member name="P:LicenseManager.Services.FingerprintMatchResult.IsMatch">
            <summary>
            是否匹配
            </summary>
        </member>
        <member name="P:LicenseManager.Services.FingerprintMatchResult.MatchScore">
            <summary>
            匹配度
            </summary>
        </member>
        <member name="P:LicenseManager.Services.FingerprintMatchResult.MatchedComponents">
            <summary>
            匹配的组件
            </summary>
        </member>
        <member name="P:LicenseManager.Services.FingerprintMatchResult.MismatchedComponents">
            <summary>
            不匹配的组件
            </summary>
        </member>
        <member name="P:LicenseManager.Services.FingerprintMatchResult.ComponentMatches">
            <summary>
            匹配详情
            </summary>
        </member>
        <member name="P:LicenseManager.Services.FingerprintMatchResult.ValidatedAt">
            <summary>
            验证时间
            </summary>
        </member>
        <member name="T:LicenseManager.Services.ComponentMatchInfo">
            <summary>
            组件匹配信息
            </summary>
        </member>
        <member name="P:LicenseManager.Services.ComponentMatchInfo.ComponentName">
            <summary>
            组件名称
            </summary>
        </member>
        <member name="P:LicenseManager.Services.ComponentMatchInfo.IsMatch">
            <summary>
            是否匹配
            </summary>
        </member>
        <member name="P:LicenseManager.Services.ComponentMatchInfo.StoredValue">
            <summary>
            存储的值
            </summary>
        </member>
        <member name="P:LicenseManager.Services.ComponentMatchInfo.CurrentValue">
            <summary>
            当前值
            </summary>
        </member>
        <member name="P:LicenseManager.Services.ComponentMatchInfo.Similarity">
            <summary>
            相似度
            </summary>
        </member>
        <member name="T:LicenseManager.Services.ProcessorInfo">
            <summary>
            处理器信息
            </summary>
        </member>
        <member name="P:LicenseManager.Services.ProcessorInfo.ProcessorId">
            <summary>
            处理器ID
            </summary>
        </member>
        <member name="P:LicenseManager.Services.ProcessorInfo.Name">
            <summary>
            处理器名称
            </summary>
        </member>
        <member name="P:LicenseManager.Services.ProcessorInfo.Manufacturer">
            <summary>
            制造商
            </summary>
        </member>
        <member name="P:LicenseManager.Services.ProcessorInfo.Architecture">
            <summary>
            架构
            </summary>
        </member>
        <member name="P:LicenseManager.Services.ProcessorInfo.CoreCount">
            <summary>
            核心数
            </summary>
        </member>
        <member name="P:LicenseManager.Services.ProcessorInfo.ThreadCount">
            <summary>
            线程数
            </summary>
        </member>
        <member name="P:LicenseManager.Services.ProcessorInfo.BaseFrequency">
            <summary>
            基础频率（MHz）
            </summary>
        </member>
        <member name="P:LicenseManager.Services.ProcessorInfo.MaxFrequency">
            <summary>
            最大频率（MHz）
            </summary>
        </member>
        <member name="T:LicenseManager.Services.MotherboardInfo">
            <summary>
            主板信息
            </summary>
        </member>
        <member name="P:LicenseManager.Services.MotherboardInfo.SerialNumber">
            <summary>
            主板序列号
            </summary>
        </member>
        <member name="P:LicenseManager.Services.MotherboardInfo.Manufacturer">
            <summary>
            制造商
            </summary>
        </member>
        <member name="P:LicenseManager.Services.MotherboardInfo.Product">
            <summary>
            产品名称
            </summary>
        </member>
        <member name="P:LicenseManager.Services.MotherboardInfo.Version">
            <summary>
            版本
            </summary>
        </member>
        <member name="T:LicenseManager.Services.DiskInfo">
            <summary>
            硬盘信息
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DiskInfo.SerialNumber">
            <summary>
            硬盘序列号
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DiskInfo.Model">
            <summary>
            型号
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DiskInfo.Manufacturer">
            <summary>
            制造商
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DiskInfo.Capacity">
            <summary>
            容量（字节）
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DiskInfo.InterfaceType">
            <summary>
            接口类型
            </summary>
        </member>
        <member name="P:LicenseManager.Services.DiskInfo.IsSystemDisk">
            <summary>
            是否为系统盘
            </summary>
        </member>
        <member name="T:LicenseManager.Services.NetworkAdapterInfo">
            <summary>
            网络适配器信息
            </summary>
        </member>
        <member name="P:LicenseManager.Services.NetworkAdapterInfo.MacAddress">
            <summary>
            MAC地址
            </summary>
        </member>
        <member name="P:LicenseManager.Services.NetworkAdapterInfo.Name">
            <summary>
            适配器名称
            </summary>
        </member>
        <member name="P:LicenseManager.Services.NetworkAdapterInfo.Description">
            <summary>
            描述
            </summary>
        </member>
        <member name="P:LicenseManager.Services.NetworkAdapterInfo.IsEnabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:LicenseManager.Services.NetworkAdapterInfo.NetworkType">
            <summary>
            网络类型
            </summary>
        </member>
        <member name="T:LicenseManager.Services.MemoryInfo">
            <summary>
            内存信息
            </summary>
        </member>
        <member name="P:LicenseManager.Services.MemoryInfo.TotalMemory">
            <summary>
            总内存（字节）
            </summary>
        </member>
        <member name="P:LicenseManager.Services.MemoryInfo.AvailableMemory">
            <summary>
            可用内存（字节）
            </summary>
        </member>
        <member name="P:LicenseManager.Services.MemoryInfo.Modules">
            <summary>
            内存条信息
            </summary>
        </member>
        <member name="T:LicenseManager.Services.MemoryModuleInfo">
            <summary>
            内存条信息
            </summary>
        </member>
        <member name="P:LicenseManager.Services.MemoryModuleInfo.SerialNumber">
            <summary>
            序列号
            </summary>
        </member>
        <member name="P:LicenseManager.Services.MemoryModuleInfo.Capacity">
            <summary>
            容量（字节）
            </summary>
        </member>
        <member name="P:LicenseManager.Services.MemoryModuleInfo.Manufacturer">
            <summary>
            制造商
            </summary>
        </member>
        <member name="P:LicenseManager.Services.MemoryModuleInfo.Speed">
            <summary>
            速度（MHz）
            </summary>
        </member>
        <member name="T:LicenseManager.Services.GraphicsInfo">
            <summary>
            显卡信息
            </summary>
        </member>
        <member name="P:LicenseManager.Services.GraphicsInfo.Name">
            <summary>
            显卡名称
            </summary>
        </member>
        <member name="P:LicenseManager.Services.GraphicsInfo.Manufacturer">
            <summary>
            制造商
            </summary>
        </member>
        <member name="P:LicenseManager.Services.GraphicsInfo.VideoMemory">
            <summary>
            显存大小（字节）
            </summary>
        </member>
        <member name="P:LicenseManager.Services.GraphicsInfo.DriverVersion">
            <summary>
            驱动版本
            </summary>
        </member>
        <member name="T:LicenseManager.Services.BiosInfo">
            <summary>
            BIOS信息
            </summary>
        </member>
        <member name="P:LicenseManager.Services.BiosInfo.SerialNumber">
            <summary>
            BIOS序列号
            </summary>
        </member>
        <member name="P:LicenseManager.Services.BiosInfo.Manufacturer">
            <summary>
            制造商
            </summary>
        </member>
        <member name="P:LicenseManager.Services.BiosInfo.Version">
            <summary>
            版本
            </summary>
        </member>
        <member name="P:LicenseManager.Services.BiosInfo.ReleaseDate">
            <summary>
            发布日期
            </summary>
        </member>
        <member name="T:LicenseManager.Services.OperatingSystemInfo">
            <summary>
            操作系统信息
            </summary>
        </member>
        <member name="P:LicenseManager.Services.OperatingSystemInfo.Name">
            <summary>
            操作系统名称
            </summary>
        </member>
        <member name="P:LicenseManager.Services.OperatingSystemInfo.Version">
            <summary>
            版本
            </summary>
        </member>
        <member name="P:LicenseManager.Services.OperatingSystemInfo.Architecture">
            <summary>
            架构
            </summary>
        </member>
        <member name="P:LicenseManager.Services.OperatingSystemInfo.InstallDate">
            <summary>
            安装日期
            </summary>
        </member>
        <member name="P:LicenseManager.Services.OperatingSystemInfo.SerialNumber">
            <summary>
            序列号
            </summary>
        </member>
        <member name="T:LicenseManager.Services.VirtualMachineDetectionResult">
            <summary>
            虚拟机检测结果
            </summary>
        </member>
        <member name="P:LicenseManager.Services.VirtualMachineDetectionResult.IsVirtualMachine">
            <summary>
            是否为虚拟机
            </summary>
        </member>
        <member name="P:LicenseManager.Services.VirtualMachineDetectionResult.VirtualMachineType">
            <summary>
            虚拟机类型
            </summary>
        </member>
        <member name="P:LicenseManager.Services.VirtualMachineDetectionResult.DetectionMethods">
            <summary>
            检测方法
            </summary>
        </member>
        <member name="P:LicenseManager.Services.VirtualMachineDetectionResult.Confidence">
            <summary>
            置信度（0.0-1.0）
            </summary>
        </member>
        <member name="T:LicenseManager.Services.HardwareChangeHistory">
            <summary>
            硬件变更历史
            </summary>
        </member>
        <member name="P:LicenseManager.Services.HardwareChangeHistory.Id">
            <summary>
            变更ID
            </summary>
        </member>
        <member name="P:LicenseManager.Services.HardwareChangeHistory.FingerprintId">
            <summary>
            指纹ID
            </summary>
        </member>
        <member name="P:LicenseManager.Services.HardwareChangeHistory.ChangeType">
            <summary>
            变更类型
            </summary>
        </member>
        <member name="P:LicenseManager.Services.HardwareChangeHistory.Description">
            <summary>
            变更描述
            </summary>
        </member>
        <member name="P:LicenseManager.Services.HardwareChangeHistory.OldValue">
            <summary>
            旧值
            </summary>
        </member>
        <member name="P:LicenseManager.Services.HardwareChangeHistory.NewValue">
            <summary>
            新值
            </summary>
        </member>
        <member name="P:LicenseManager.Services.HardwareChangeHistory.ChangeTime">
            <summary>
            变更时间
            </summary>
        </member>
        <member name="T:LicenseManager.Services.HardwareFingerprintReport">
            <summary>
            硬件指纹报告
            </summary>
        </member>
        <member name="P:LicenseManager.Services.HardwareFingerprintReport.Summary">
            <summary>
            指纹摘要
            </summary>
        </member>
        <member name="P:LicenseManager.Services.HardwareFingerprintReport.CompletenessScore">
            <summary>
            完整性评分
            </summary>
        </member>
        <member name="P:LicenseManager.Services.HardwareFingerprintReport.UniquenessScore">
            <summary>
            唯一性评分
            </summary>
        </member>
        <member name="P:LicenseManager.Services.HardwareFingerprintReport.StabilityScore">
            <summary>
            稳定性评分
            </summary>
        </member>
        <member name="P:LicenseManager.Services.HardwareFingerprintReport.ComponentReports">
            <summary>
            组件详情
            </summary>
        </member>
        <member name="P:LicenseManager.Services.HardwareFingerprintReport.Recommendations">
            <summary>
            建议
            </summary>
        </member>
        <member name="T:LicenseManager.Services.ComponentReport">
            <summary>
            组件报告
            </summary>
        </member>
        <member name="P:LicenseManager.Services.ComponentReport.Name">
            <summary>
            组件名称
            </summary>
        </member>
        <member name="P:LicenseManager.Services.ComponentReport.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:LicenseManager.Services.ComponentReport.Value">
            <summary>
            值
            </summary>
        </member>
        <member name="P:LicenseManager.Services.ComponentReport.ReliabilityScore">
            <summary>
            可靠性评分
            </summary>
        </member>
        <member name="T:LicenseManager.Services.FingerprintExportResult">
            <summary>
            指纹导出结果
            </summary>
        </member>
        <member name="P:LicenseManager.Services.FingerprintExportResult.Success">
            <summary>
            是否成功
            </summary>
        </member>
        <member name="P:LicenseManager.Services.FingerprintExportResult.Data">
            <summary>
            导出数据
            </summary>
        </member>
        <member name="P:LicenseManager.Services.FingerprintExportResult.FileName">
            <summary>
            文件名
            </summary>
        </member>
        <member name="P:LicenseManager.Services.FingerprintExportResult.ErrorMessage">
            <summary>
            错误消息
            </summary>
        </member>
        <member name="T:LicenseManager.Services.FingerprintImportResult">
            <summary>
            指纹导入结果
            </summary>
        </member>
        <member name="P:LicenseManager.Services.FingerprintImportResult.Success">
            <summary>
            是否成功
            </summary>
        </member>
        <member name="P:LicenseManager.Services.FingerprintImportResult.Fingerprint">
            <summary>
            导入的指纹
            </summary>
        </member>
        <member name="P:LicenseManager.Services.FingerprintImportResult.ErrorMessage">
            <summary>
            错误消息
            </summary>
        </member>
        <member name="P:LicenseManager.Services.FingerprintImportResult.Warnings">
            <summary>
            警告消息
            </summary>
        </member>
        <member name="T:LicenseManager.Services.FingerprintOptimizationOptions">
            <summary>
            指纹优化选项
            </summary>
        </member>
        <member name="P:LicenseManager.Services.FingerprintOptimizationOptions.TargetStrengthLevel">
            <summary>
            目标强度等级
            </summary>
        </member>
        <member name="P:LicenseManager.Services.FingerprintOptimizationOptions.OptimizePerformance">
            <summary>
            是否优化性能
            </summary>
        </member>
        <member name="P:LicenseManager.Services.FingerprintOptimizationOptions.OptimizeStability">
            <summary>
            是否优化稳定性
            </summary>
        </member>
        <member name="P:LicenseManager.Services.FingerprintOptimizationOptions.ExcludedComponents">
            <summary>
            排除的组件
            </summary>
        </member>
        <member name="T:LicenseManager.Services.FingerprintExportFormat">
            <summary>
            指纹导出格式
            </summary>
        </member>
        <member name="T:LicenseManager.Services.ILicenseService">
            <summary>
            许可证服务接口
            提供许可证生成、验证、管理等核心业务功能
            </summary>
        </member>
        <member name="M:LicenseManager.Services.ILicenseService.GenerateLicenseAsync(LicenseManager.Services.LicenseGenerationRequest)">
            <summary>
            生成许可证
            </summary>
            <param name="request">许可证生成请求</param>
            <returns>生成的许可证</returns>
        </member>
        <member name="M:LicenseManager.Services.ILicenseService.GenerateLicensesBatchAsync(System.Collections.Generic.IEnumerable{LicenseManager.Services.LicenseGenerationRequest})">
            <summary>
            批量生成许可证
            </summary>
            <param name="requests">许可证生成请求列表</param>
            <returns>生成结果列表</returns>
        </member>
        <member name="M:LicenseManager.Services.ILicenseService.ValidateLicenseAsync(System.String,System.String,System.String)">
            <summary>
            验证许可证
            </summary>
            <param name="licenseKey">许可证密钥</param>
            <param name="hardwareFingerprint">硬件指纹（可选）</param>
            <param name="appId">应用程序ID（可选）</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:LicenseManager.Services.ILicenseService.ActivateLicenseAsync(System.String,System.String)">
            <summary>
            激活许可证
            </summary>
            <param name="licenseKey">许可证密钥</param>
            <param name="hardwareFingerprint">硬件指纹</param>
            <returns>激活结果</returns>
        </member>
        <member name="M:LicenseManager.Services.ILicenseService.DeactivateLicenseAsync(System.Int32,System.String)">
            <summary>
            停用许可证
            </summary>
            <param name="licenseId">许可证ID</param>
            <param name="reason">停用原因</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:LicenseManager.Services.ILicenseService.ExtendLicenseAsync(System.Int32,System.Int32)">
            <summary>
            延长许可证有效期
            </summary>
            <param name="licenseId">许可证ID</param>
            <param name="additionalDays">延长天数</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:LicenseManager.Services.ILicenseService.UpdateLicenseAsync(System.Int32,LicenseManager.Services.LicenseUpdateRequest)">
            <summary>
            更新许可证信息
            </summary>
            <param name="licenseId">许可证ID</param>
            <param name="updateRequest">更新请求</param>
            <returns>更新后的许可证</returns>
        </member>
        <member name="M:LicenseManager.Services.ILicenseService.DeleteLicenseAsync(System.Int32)">
            <summary>
            删除许可证
            </summary>
            <param name="licenseId">许可证ID</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:LicenseManager.Services.ILicenseService.GetLicenseDetailAsync(System.Int32)">
            <summary>
            获取许可证详细信息
            </summary>
            <param name="licenseId">许可证ID</param>
            <returns>许可证详细信息</returns>
        </member>
        <member name="M:LicenseManager.Services.ILicenseService.SearchLicensesAsync(LicenseManager.Services.LicenseSearchRequest)">
            <summary>
            搜索许可证
            </summary>
            <param name="searchRequest">搜索请求</param>
            <returns>搜索结果</returns>
        </member>
        <member name="M:LicenseManager.Services.ILicenseService.GetLicenseStatisticsAsync">
            <summary>
            获取许可证统计信息
            </summary>
            <returns>统计信息</returns>
        </member>
        <member name="M:LicenseManager.Services.ILicenseService.ExportLicenseFileAsync(System.Int32,LicenseManager.Services.LicenseFileFormat)">
            <summary>
            导出许可证文件
            </summary>
            <param name="licenseId">许可证ID</param>
            <param name="format">导出格式</param>
            <returns>许可证文件内容</returns>
        </member>
        <member name="M:LicenseManager.Services.ILicenseService.ImportLicenseFileAsync(System.Byte[],System.String)">
            <summary>
            导入许可证文件
            </summary>
            <param name="fileContent">文件内容</param>
            <param name="fileName">文件名</param>
            <returns>导入结果</returns>
        </member>
        <member name="M:LicenseManager.Services.ILicenseService.CheckLicenseConflictAsync(LicenseManager.Services.LicenseGenerationRequest)">
            <summary>
            检查许可证冲突
            </summary>
            <param name="request">许可证生成请求</param>
            <returns>冲突检查结果</returns>
        </member>
        <member name="M:LicenseManager.Services.ILicenseService.GetLicenseUsageHistoryAsync(System.Int32,System.Int32,System.Int32)">
            <summary>
            获取许可证使用历史
            </summary>
            <param name="licenseId">许可证ID</param>
            <param name="pageIndex">页索引</param>
            <param name="pageSize">页大小</param>
            <returns>使用历史</returns>
        </member>
        <member name="T:LicenseManager.Services.LicenseGenerationRequest">
            <summary>
            许可证生成请求
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseGenerationRequest.AppId">
            <summary>
            应用程序ID
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseGenerationRequest.AppName">
            <summary>
            应用程序名称
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseGenerationRequest.AuthorizationTypeId">
            <summary>
            授权类型ID
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseGenerationRequest.CustomerName">
            <summary>
            客户名称
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseGenerationRequest.CustomerEmail">
            <summary>
            客户邮箱
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseGenerationRequest.ValidityDays">
            <summary>
            有效期天数（如果不指定则使用授权类型的默认值）
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseGenerationRequest.MaxUsers">
            <summary>
            最大用户数量
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseGenerationRequest.HardwareFingerprint">
            <summary>
            硬件指纹（用于硬件绑定）
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseGenerationRequest.Features">
            <summary>
            功能特性列表
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseGenerationRequest.CustomProperties">
            <summary>
            自定义属性
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseGenerationRequest.Notes">
            <summary>
            备注信息
            </summary>
        </member>
        <member name="T:LicenseManager.Services.LicenseGenerationResult">
            <summary>
            许可证生成结果
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseGenerationResult.Success">
            <summary>
            是否成功
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseGenerationResult.License">
            <summary>
            生成的许可证
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseGenerationResult.LicenseKey">
            <summary>
            许可证密钥
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseGenerationResult.LicenseFileContent">
            <summary>
            许可证文件内容
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseGenerationResult.ErrorMessage">
            <summary>
            错误消息
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseGenerationResult.ErrorCode">
            <summary>
            错误代码
            </summary>
        </member>
        <member name="T:LicenseManager.Services.LicenseActivationResult">
            <summary>
            许可证激活结果
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseActivationResult.Success">
            <summary>
            是否成功
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseActivationResult.License">
            <summary>
            许可证信息
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseActivationResult.ActivationToken">
            <summary>
            激活令牌
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseActivationResult.Message">
            <summary>
            消息
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseActivationResult.ErrorCode">
            <summary>
            错误代码
            </summary>
        </member>
        <member name="T:LicenseManager.Services.LicenseUpdateRequest">
            <summary>
            许可证更新请求
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseUpdateRequest.CustomerName">
            <summary>
            客户名称
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseUpdateRequest.CustomerEmail">
            <summary>
            客户邮箱
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseUpdateRequest.MaxUsers">
            <summary>
            最大用户数量
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseUpdateRequest.Features">
            <summary>
            功能特性
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseUpdateRequest.Notes">
            <summary>
            备注信息
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseUpdateRequest.IsActive">
            <summary>
            是否激活
            </summary>
        </member>
        <member name="T:LicenseManager.Services.LicenseSearchRequest">
            <summary>
            许可证搜索请求
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseSearchRequest.SearchTerm">
            <summary>
            搜索关键词
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseSearchRequest.AppId">
            <summary>
            应用程序ID
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseSearchRequest.AuthorizationTypeId">
            <summary>
            授权类型ID
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseSearchRequest.IsActive">
            <summary>
            是否激活
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseSearchRequest.IsExpired">
            <summary>
            是否过期
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseSearchRequest.StartDate">
            <summary>
            开始日期
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseSearchRequest.EndDate">
            <summary>
            结束日期
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseSearchRequest.PageIndex">
            <summary>
            页索引
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseSearchRequest.PageSize">
            <summary>
            页大小
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseSearchRequest.SortBy">
            <summary>
            排序字段
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseSearchRequest.Ascending">
            <summary>
            是否升序
            </summary>
        </member>
        <member name="T:LicenseManager.Services.LicenseDetailInfo">
            <summary>
            许可证详细信息
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseDetailInfo.License">
            <summary>
            许可证基本信息
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseDetailInfo.AuthorizationType">
            <summary>
            授权类型信息
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseDetailInfo.LicenseInfos">
            <summary>
            扩展属性列表
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseDetailInfo.HardwareFingerprint">
            <summary>
            硬件指纹信息
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseDetailInfo.UsageStatistics">
            <summary>
            使用统计
            </summary>
        </member>
        <member name="T:LicenseManager.Services.LicenseUsageStatistics">
            <summary>
            许可证使用统计
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseUsageStatistics.TotalValidations">
            <summary>
            总验证次数
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseUsageStatistics.SuccessfulValidations">
            <summary>
            成功验证次数
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseUsageStatistics.FailedValidations">
            <summary>
            失败验证次数
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseUsageStatistics.LastValidationTime">
            <summary>
            最后验证时间
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseUsageStatistics.LastValidationIp">
            <summary>
            最后验证IP
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseUsageStatistics.ActivationCount">
            <summary>
            激活次数
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseUsageStatistics.LastActivationTime">
            <summary>
            最后激活时间
            </summary>
        </member>
        <member name="T:LicenseManager.Services.LicenseFileResult">
            <summary>
            许可证文件结果
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseFileResult.Content">
            <summary>
            文件内容
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseFileResult.FileName">
            <summary>
            文件名
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseFileResult.ContentType">
            <summary>
            内容类型
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseFileResult.FileSize">
            <summary>
            文件大小
            </summary>
        </member>
        <member name="T:LicenseManager.Services.LicenseImportResult">
            <summary>
            许可证导入结果
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseImportResult.Success">
            <summary>
            是否成功
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseImportResult.License">
            <summary>
            导入的许可证
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseImportResult.ErrorMessage">
            <summary>
            错误消息
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseImportResult.Warnings">
            <summary>
            警告消息
            </summary>
        </member>
        <member name="T:LicenseManager.Services.LicenseConflictResult">
            <summary>
            许可证冲突结果
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseConflictResult.HasConflict">
            <summary>
            是否有冲突
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseConflictResult.ConflictType">
            <summary>
            冲突类型
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseConflictResult.ConflictDescription">
            <summary>
            冲突描述
            </summary>
        </member>
        <member name="P:LicenseManager.Services.LicenseConflictResult.ConflictingLicenses">
            <summary>
            冲突的许可证列表
            </summary>
        </member>
        <member name="T:LicenseManager.Services.LicenseConflictType">
            <summary>
            许可证冲突类型
            </summary>
        </member>
        <member name="T:LicenseManager.Services.LicenseFileFormat">
            <summary>
            许可证文件格式
            </summary>
        </member>
        <member name="T:LicenseManager.Services.IValidationService">
            <summary>
            验证服务接口
            提供各种验证功能，包括许可证验证、数据验证等
            </summary>
        </member>
        <member name="M:LicenseManager.Services.IValidationService.ValidateLicenseBasicInfo(LicenseManager.Models.License)">
            <summary>
            验证许可证基本信息
            </summary>
            <param name="license">许可证对象</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:LicenseManager.Services.IValidationService.ValidateLicenseExpiry(LicenseManager.Models.License)">
            <summary>
            验证许可证是否过期
            </summary>
            <param name="license">许可证对象</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:LicenseManager.Services.IValidationService.ValidateHardwareFingerprintAsync(LicenseManager.Models.License,System.String)">
            <summary>
            验证硬件指纹匹配
            </summary>
            <param name="license">许可证对象</param>
            <param name="currentFingerprint">当前硬件指纹</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:LicenseManager.Services.IValidationService.ValidateLicenseFeatures(LicenseManager.Models.License,System.String[])">
            <summary>
            验证许可证功能特性
            </summary>
            <param name="license">许可证对象</param>
            <param name="requiredFeatures">需要的功能特性</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:LicenseManager.Services.IValidationService.ValidateUserLimit(LicenseManager.Models.License,System.Int32)">
            <summary>
            验证用户数量限制
            </summary>
            <param name="license">许可证对象</param>
            <param name="currentUserCount">当前用户数量</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:LicenseManager.Services.IValidationService.ValidateLicenseIntegrityAsync(System.String,System.String)">
            <summary>
            验证许可证完整性
            </summary>
            <param name="licenseKey">许可证密钥</param>
            <param name="publicKey">公钥</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:LicenseManager.Services.IValidationService.ValidateApplicationCompatibility(LicenseManager.Models.License,System.String,System.String)">
            <summary>
            验证应用程序兼容性
            </summary>
            <param name="license">许可证对象</param>
            <param name="appId">应用程序ID</param>
            <param name="appVersion">应用程序版本</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:LicenseManager.Services.IValidationService.ValidateNetworkLicenseAsync(System.String,System.String)">
            <summary>
            验证网络许可证
            </summary>
            <param name="licenseKey">许可证密钥</param>
            <param name="serverEndpoint">许可证服务器端点</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:LicenseManager.Services.IValidationService.ValidateLicenseBlacklistAsync(System.String)">
            <summary>
            验证许可证黑名单
            </summary>
            <param name="licenseKey">许可证密钥</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:LicenseManager.Services.IValidationService.ValidateCustomerInfo(System.String,System.String)">
            <summary>
            验证客户信息
            </summary>
            <param name="customerName">客户名称</param>
            <param name="customerEmail">客户邮箱</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:LicenseManager.Services.IValidationService.ValidateAuthorizationType(LicenseManager.Models.AuthorizationType)">
            <summary>
            验证授权类型
            </summary>
            <param name="authorizationType">授权类型</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:LicenseManager.Services.IValidationService.ValidateAppTemplate(LicenseManager.Models.AppTemplate)">
            <summary>
            验证应用程序模板
            </summary>
            <param name="appTemplate">应用程序模板</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:LicenseManager.Services.IValidationService.ValidateHardwareFingerprintFormat(System.String)">
            <summary>
            验证硬件指纹格式
            </summary>
            <param name="hardwareFingerprint">硬件指纹</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:LicenseManager.Services.IValidationService.ValidateLicenseGenerationRequestAsync(LicenseManager.Services.LicenseGenerationRequest)">
            <summary>
            验证许可证生成请求
            </summary>
            <param name="request">许可证生成请求</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:LicenseManager.Services.IValidationService.ValidateLicensesBatchAsync(System.Collections.Generic.IEnumerable{System.String},LicenseManager.Services.ValidationOptions)">
            <summary>
            批量验证许可证
            </summary>
            <param name="licenseKeys">许可证密钥列表</param>
            <param name="validationOptions">验证选项</param>
            <returns>批量验证结果</returns>
        </member>
        <member name="M:LicenseManager.Services.IValidationService.ValidateLicenseFileFormat(System.Byte[],System.String)">
            <summary>
            验证许可证文件格式
            </summary>
            <param name="fileContent">文件内容</param>
            <param name="fileName">文件名</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:LicenseManager.Services.IValidationService.ValidateSystemEnvironment(LicenseManager.Models.License)">
            <summary>
            验证系统环境
            </summary>
            <param name="license">许可证对象</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:LicenseManager.Services.IValidationService.ValidateTimeSynchronizationAsync">
            <summary>
            验证时间同步
            </summary>
            <returns>验证结果</returns>
        </member>
        <member name="M:LicenseManager.Services.IValidationService.GetValidationRulesAsync(LicenseManager.Services.ValidationRuleType)">
            <summary>
            获取验证规则
            </summary>
            <param name="ruleType">规则类型</param>
            <returns>验证规则列表</returns>
        </member>
        <member name="M:LicenseManager.Services.IValidationService.UpdateValidationRuleAsync(LicenseManager.Services.ValidationRule)">
            <summary>
            更新验证规则
            </summary>
            <param name="rule">验证规则</param>
            <returns>是否成功</returns>
        </member>
        <member name="T:LicenseManager.Services.ValidationResult">
            <summary>
            验证结果
            </summary>
        </member>
        <member name="P:LicenseManager.Services.ValidationResult.IsValid">
            <summary>
            是否有效
            </summary>
        </member>
        <member name="P:LicenseManager.Services.ValidationResult.Message">
            <summary>
            验证消息
            </summary>
        </member>
        <member name="P:LicenseManager.Services.ValidationResult.ErrorCode">
            <summary>
            错误代码
            </summary>
        </member>
        <member name="P:LicenseManager.Services.ValidationResult.Level">
            <summary>
            验证级别
            </summary>
        </member>
        <member name="P:LicenseManager.Services.ValidationResult.Details">
            <summary>
            详细信息
            </summary>
        </member>
        <member name="P:LicenseManager.Services.ValidationResult.ValidatedAt">
            <summary>
            验证时间
            </summary>
        </member>
        <member name="P:LicenseManager.Services.ValidationResult.SubResults">
            <summary>
            子验证结果
            </summary>
        </member>
        <member name="M:LicenseManager.Services.ValidationResult.Success(System.String)">
            <summary>
            创建成功的验证结果
            </summary>
            <param name="message">消息</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:LicenseManager.Services.ValidationResult.Failure(System.String,System.String)">
            <summary>
            创建失败的验证结果
            </summary>
            <param name="message">错误消息</param>
            <param name="errorCode">错误代码</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:LicenseManager.Services.ValidationResult.Warning(System.String)">
            <summary>
            创建警告的验证结果
            </summary>
            <param name="message">警告消息</param>
            <returns>验证结果</returns>
        </member>
        <member name="T:LicenseManager.Services.BatchValidationResult">
            <summary>
            批量验证结果
            </summary>
        </member>
        <member name="P:LicenseManager.Services.BatchValidationResult.TotalCount">
            <summary>
            总数量
            </summary>
        </member>
        <member name="P:LicenseManager.Services.BatchValidationResult.SuccessCount">
            <summary>
            成功数量
            </summary>
        </member>
        <member name="P:LicenseManager.Services.BatchValidationResult.FailureCount">
            <summary>
            失败数量
            </summary>
        </member>
        <member name="P:LicenseManager.Services.BatchValidationResult.Results">
            <summary>
            详细结果
            </summary>
        </member>
        <member name="P:LicenseManager.Services.BatchValidationResult.ValidatedAt">
            <summary>
            验证时间
            </summary>
        </member>
        <member name="P:LicenseManager.Services.BatchValidationResult.ElapsedMilliseconds">
            <summary>
            验证耗时（毫秒）
            </summary>
        </member>
        <member name="P:LicenseManager.Services.BatchValidationResult.SuccessRate">
            <summary>
            成功率
            </summary>
        </member>
        <member name="T:LicenseManager.Services.ValidationOptions">
            <summary>
            验证选项
            </summary>
        </member>
        <member name="P:LicenseManager.Services.ValidationOptions.ValidateExpiry">
            <summary>
            是否验证过期时间
            </summary>
        </member>
        <member name="P:LicenseManager.Services.ValidationOptions.ValidateHardwareFingerprint">
            <summary>
            是否验证硬件指纹
            </summary>
        </member>
        <member name="P:LicenseManager.Services.ValidationOptions.ValidateIntegrity">
            <summary>
            是否验证完整性
            </summary>
        </member>
        <member name="P:LicenseManager.Services.ValidationOptions.ValidateBlacklist">
            <summary>
            是否验证黑名单
            </summary>
        </member>
        <member name="P:LicenseManager.Services.ValidationOptions.ValidateNetworkLicense">
            <summary>
            是否验证网络许可证
            </summary>
        </member>
        <member name="P:LicenseManager.Services.ValidationOptions.TimeoutSeconds">
            <summary>
            验证超时时间（秒）
            </summary>
        </member>
        <member name="P:LicenseManager.Services.ValidationOptions.ConcurrentValidations">
            <summary>
            并发验证数量
            </summary>
        </member>
        <member name="P:LicenseManager.Services.ValidationOptions.SkipAlreadyValidated">
            <summary>
            是否跳过已验证的许可证
            </summary>
        </member>
        <member name="T:LicenseManager.Services.ValidationRule">
            <summary>
            验证规则
            </summary>
        </member>
        <member name="P:LicenseManager.Services.ValidationRule.Id">
            <summary>
            规则ID
            </summary>
        </member>
        <member name="P:LicenseManager.Services.ValidationRule.Name">
            <summary>
            规则名称
            </summary>
        </member>
        <member name="P:LicenseManager.Services.ValidationRule.Type">
            <summary>
            规则类型
            </summary>
        </member>
        <member name="P:LicenseManager.Services.ValidationRule.Expression">
            <summary>
            规则表达式
            </summary>
        </member>
        <member name="P:LicenseManager.Services.ValidationRule.ErrorMessage">
            <summary>
            错误消息
            </summary>
        </member>
        <member name="P:LicenseManager.Services.ValidationRule.IsEnabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:LicenseManager.Services.ValidationRule.Priority">
            <summary>
            优先级
            </summary>
        </member>
        <member name="P:LicenseManager.Services.ValidationRule.CreatedAt">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="T:LicenseManager.Services.ValidationLevel">
            <summary>
            验证级别
            </summary>
        </member>
        <member name="T:LicenseManager.Services.ValidationRuleType">
            <summary>
            验证规则类型
            </summary>
        </member>
        <member name="T:LicenseManager.Utils.CryptoHelper">
            <summary>
            加密工具类
            提供各种加密、解密、哈希等功能的静态方法
            </summary>
        </member>
        <member name="M:LicenseManager.Utils.CryptoHelper.GenerateRsaKeyPair(System.Int32)">
            <summary>
            生成RSA密钥对
            </summary>
            <param name="keySize">密钥长度（位）</param>
            <returns>RSA密钥对</returns>
        </member>
        <member name="M:LicenseManager.Utils.CryptoHelper.RsaEncrypt(System.Byte[],System.String)">
            <summary>
            RSA加密
            </summary>
            <param name="data">要加密的数据</param>
            <param name="publicKey">公钥（Base64格式）</param>
            <returns>加密后的数据</returns>
        </member>
        <member name="M:LicenseManager.Utils.CryptoHelper.RsaDecrypt(System.Byte[],System.String)">
            <summary>
            RSA解密
            </summary>
            <param name="encryptedData">加密的数据</param>
            <param name="privateKey">私钥（Base64格式）</param>
            <returns>解密后的数据</returns>
        </member>
        <member name="M:LicenseManager.Utils.CryptoHelper.RsaSign(System.Byte[],System.String)">
            <summary>
            RSA签名
            </summary>
            <param name="data">要签名的数据</param>
            <param name="privateKey">私钥（Base64格式）</param>
            <returns>签名</returns>
        </member>
        <member name="M:LicenseManager.Utils.CryptoHelper.RsaVerifySignature(System.Byte[],System.Byte[],System.String)">
            <summary>
            RSA验证签名
            </summary>
            <param name="data">原始数据</param>
            <param name="signature">签名</param>
            <param name="publicKey">公钥（Base64格式）</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:LicenseManager.Utils.CryptoHelper.GenerateAesKey(System.Int32)">
            <summary>
            生成AES密钥和IV
            </summary>
            <param name="keySize">密钥长度（位）</param>
            <returns>密钥和IV</returns>
        </member>
        <member name="M:LicenseManager.Utils.CryptoHelper.AesEncrypt(System.Byte[],System.Byte[],System.Byte[])">
            <summary>
            AES加密
            </summary>
            <param name="data">要加密的数据</param>
            <param name="key">密钥</param>
            <param name="iv">初始化向量</param>
            <returns>加密后的数据</returns>
        </member>
        <member name="M:LicenseManager.Utils.CryptoHelper.AesDecrypt(System.Byte[],System.Byte[],System.Byte[])">
            <summary>
            AES解密
            </summary>
            <param name="encryptedData">加密的数据</param>
            <param name="key">密钥</param>
            <param name="iv">初始化向量</param>
            <returns>解密后的数据</returns>
        </member>
        <member name="M:LicenseManager.Utils.CryptoHelper.ComputeSha256Hash(System.Byte[])">
            <summary>
            计算SHA256哈希
            </summary>
            <param name="data">要计算哈希的数据</param>
            <returns>哈希值</returns>
        </member>
        <member name="M:LicenseManager.Utils.CryptoHelper.ComputeSha256Hash(System.String)">
            <summary>
            计算SHA256哈希（字符串）
            </summary>
            <param name="input">输入字符串</param>
            <returns>哈希值（十六进制字符串）</returns>
        </member>
        <member name="M:LicenseManager.Utils.CryptoHelper.ComputeMd5Hash(System.Byte[])">
            <summary>
            计算MD5哈希
            </summary>
            <param name="data">要计算哈希的数据</param>
            <returns>哈希值</returns>
        </member>
        <member name="M:LicenseManager.Utils.CryptoHelper.ComputeMd5Hash(System.String)">
            <summary>
            计算MD5哈希（字符串）
            </summary>
            <param name="input">输入字符串</param>
            <returns>哈希值（十六进制字符串）</returns>
        </member>
        <member name="M:LicenseManager.Utils.CryptoHelper.GenerateRandomBytes(System.Int32)">
            <summary>
            生成随机字节
            </summary>
            <param name="length">字节长度</param>
            <returns>随机字节数组</returns>
        </member>
        <member name="M:LicenseManager.Utils.CryptoHelper.GenerateRandomString(System.Int32,System.Boolean)">
            <summary>
            生成随机字符串
            </summary>
            <param name="length">字符串长度</param>
            <param name="includeSpecialChars">是否包含特殊字符</param>
            <returns>随机字符串</returns>
        </member>
        <member name="M:LicenseManager.Utils.CryptoHelper.ToBase64String(System.Byte[])">
            <summary>
            Base64编码
            </summary>
            <param name="data">要编码的数据</param>
            <returns>Base64字符串</returns>
        </member>
        <member name="M:LicenseManager.Utils.CryptoHelper.FromBase64String(System.String)">
            <summary>
            Base64解码
            </summary>
            <param name="base64String">Base64字符串</param>
            <returns>解码后的数据</returns>
        </member>
        <member name="M:LicenseManager.Utils.CryptoHelper.ToHexString(System.Byte[])">
            <summary>
            十六进制编码
            </summary>
            <param name="data">要编码的数据</param>
            <returns>十六进制字符串</returns>
        </member>
        <member name="M:LicenseManager.Utils.CryptoHelper.FromHexString(System.String)">
            <summary>
            十六进制解码
            </summary>
            <param name="hexString">十六进制字符串</param>
            <returns>解码后的数据</returns>
        </member>
        <member name="M:LicenseManager.Utils.CryptoHelper.HashPassword(System.String,System.Byte[],System.Int32,System.Int32)">
            <summary>
            密码哈希（使用PBKDF2）
            </summary>
            <param name="password">密码</param>
            <param name="salt">盐值</param>
            <param name="iterations">迭代次数</param>
            <param name="hashLength">哈希长度</param>
            <returns>密码哈希</returns>
        </member>
        <member name="M:LicenseManager.Utils.CryptoHelper.VerifyPassword(System.String,System.Byte[],System.Byte[],System.Int32)">
            <summary>
            验证密码
            </summary>
            <param name="password">密码</param>
            <param name="hash">存储的哈希</param>
            <param name="salt">盐值</param>
            <param name="iterations">迭代次数</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:LicenseManager.Utils.CryptoHelper.GenerateLicenseKey(System.String,System.Int32)">
            <summary>
            生成许可证密钥
            </summary>
            <param name="prefix">前缀</param>
            <param name="length">密钥长度</param>
            <returns>许可证密钥</returns>
        </member>
        <member name="M:LicenseManager.Utils.CryptoHelper.EncryptObjectToJson``1(``0,System.String)">
            <summary>
            加密对象为JSON
            </summary>
            <typeparam name="T">对象类型</typeparam>
            <param name="obj">要加密的对象</param>
            <param name="publicKey">公钥</param>
            <returns>加密后的Base64字符串</returns>
        </member>
        <member name="M:LicenseManager.Utils.CryptoHelper.DecryptObjectFromJson``1(System.String,System.String)">
            <summary>
            从加密的JSON解密对象
            </summary>
            <typeparam name="T">对象类型</typeparam>
            <param name="encryptedJson">加密的JSON字符串</param>
            <param name="privateKey">私钥</param>
            <returns>解密后的对象</returns>
        </member>
        <member name="M:LicenseManager.Utils.CryptoHelper.ComputeFileHashAsync(System.String)">
            <summary>
            计算文件哈希
            </summary>
            <param name="filePath">文件路径</param>
            <returns>文件SHA256哈希值</returns>
        </member>
        <member name="M:LicenseManager.Utils.CryptoHelper.SecureEquals(System.Byte[],System.Byte[])">
            <summary>
            安全比较两个字节数组
            </summary>
            <param name="a">数组A</param>
            <param name="b">数组B</param>
            <returns>是否相等</returns>
        </member>
        <member name="M:LicenseManager.Utils.CryptoHelper.ComputeHmacSha256(System.Byte[],System.Byte[])">
            <summary>
            生成HMAC签名
            </summary>
            <param name="data">要签名的数据</param>
            <param name="key">密钥</param>
            <returns>HMAC签名</returns>
        </member>
        <member name="M:LicenseManager.Utils.CryptoHelper.VerifyHmacSha256(System.Byte[],System.Byte[],System.Byte[])">
            <summary>
            验证HMAC签名
            </summary>
            <param name="data">原始数据</param>
            <param name="signature">签名</param>
            <param name="key">密钥</param>
            <returns>验证结果</returns>
        </member>
        <member name="T:LicenseManager.Utils.DateTimeHelper">
            <summary>
            日期时间工具类
            提供日期时间处理、格式化、计算等功能的静态方法
            </summary>
        </member>
        <member name="F:LicenseManager.Utils.DateTimeHelper.StandardDateFormat">
            <summary>
            标准日期格式
            </summary>
        </member>
        <member name="F:LicenseManager.Utils.DateTimeHelper.StandardDateTimeFormat">
            <summary>
            标准日期时间格式
            </summary>
        </member>
        <member name="F:LicenseManager.Utils.DateTimeHelper.Iso8601Format">
            <summary>
            ISO 8601日期时间格式
            </summary>
        </member>
        <member name="F:LicenseManager.Utils.DateTimeHelper.FileNameSafeDateTimeFormat">
            <summary>
            文件名安全的日期时间格式
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.DateTimeHelper.UtcNow">
            <summary>
            获取当前UTC时间
            </summary>
            <returns>当前UTC时间</returns>
        </member>
        <member name="P:LicenseManager.Utils.DateTimeHelper.Now">
            <summary>
            获取当前本地时间
            </summary>
            <returns>当前本地时间</returns>
        </member>
        <member name="P:LicenseManager.Utils.DateTimeHelper.StartOfToday">
            <summary>
            获取今天的开始时间（00:00:00）
            </summary>
            <returns>今天的开始时间</returns>
        </member>
        <member name="P:LicenseManager.Utils.DateTimeHelper.EndOfToday">
            <summary>
            获取今天的结束时间（23:59:59.999）
            </summary>
            <returns>今天的结束时间</returns>
        </member>
        <member name="P:LicenseManager.Utils.DateTimeHelper.StartOfWeek">
            <summary>
            获取本周的开始时间（周一00:00:00）
            </summary>
            <returns>本周的开始时间</returns>
        </member>
        <member name="P:LicenseManager.Utils.DateTimeHelper.StartOfMonth">
            <summary>
            获取本月的开始时间
            </summary>
            <returns>本月的开始时间</returns>
        </member>
        <member name="P:LicenseManager.Utils.DateTimeHelper.StartOfYear">
            <summary>
            获取本年的开始时间
            </summary>
            <returns>本年的开始时间</returns>
        </member>
        <member name="M:LicenseManager.Utils.DateTimeHelper.Format(System.DateTime,System.String)">
            <summary>
            格式化日期时间为标准格式
            </summary>
            <param name="dateTime">日期时间</param>
            <param name="format">格式字符串</param>
            <returns>格式化后的字符串</returns>
        </member>
        <member name="M:LicenseManager.Utils.DateTimeHelper.ToIso8601String(System.DateTime)">
            <summary>
            格式化日期时间为ISO 8601格式
            </summary>
            <param name="dateTime">日期时间</param>
            <returns>ISO 8601格式字符串</returns>
        </member>
        <member name="M:LicenseManager.Utils.DateTimeHelper.FromIso8601String(System.String)">
            <summary>
            从ISO 8601字符串解析日期时间
            </summary>
            <param name="iso8601String">ISO 8601格式字符串</param>
            <returns>解析后的日期时间</returns>
        </member>
        <member name="M:LicenseManager.Utils.DateTimeHelper.ParseDateTime(System.String,System.String)">
            <summary>
            解析日期时间字符串
            </summary>
            <param name="dateTimeString">日期时间字符串</param>
            <param name="format">格式字符串</param>
            <returns>解析后的日期时间</returns>
        </member>
        <member name="M:LicenseManager.Utils.DateTimeHelper.DaysBetween(System.DateTime,System.DateTime)">
            <summary>
            计算两个日期之间的天数差
            </summary>
            <param name="startDate">开始日期</param>
            <param name="endDate">结束日期</param>
            <returns>天数差</returns>
        </member>
        <member name="M:LicenseManager.Utils.DateTimeHelper.WorkDaysBetween(System.DateTime,System.DateTime)">
            <summary>
            计算两个日期之间的工作日数量
            </summary>
            <param name="startDate">开始日期</param>
            <param name="endDate">结束日期</param>
            <returns>工作日数量</returns>
        </member>
        <member name="M:LicenseManager.Utils.DateTimeHelper.AddWorkDays(System.DateTime,System.Int32)">
            <summary>
            添加工作日
            </summary>
            <param name="startDate">开始日期</param>
            <param name="workDays">工作日数量</param>
            <returns>添加工作日后的日期</returns>
        </member>
        <member name="M:LicenseManager.Utils.DateTimeHelper.IsWorkDay(System.DateTime)">
            <summary>
            检查是否为工作日
            </summary>
            <param name="date">日期</param>
            <returns>是否为工作日</returns>
        </member>
        <member name="M:LicenseManager.Utils.DateTimeHelper.IsWeekend(System.DateTime)">
            <summary>
            检查是否为周末
            </summary>
            <param name="date">日期</param>
            <returns>是否为周末</returns>
        </member>
        <member name="M:LicenseManager.Utils.DateTimeHelper.GetFirstDayOfMonth(System.DateTime)">
            <summary>
            获取月份的第一天
            </summary>
            <param name="date">日期</param>
            <returns>月份的第一天</returns>
        </member>
        <member name="M:LicenseManager.Utils.DateTimeHelper.GetLastDayOfMonth(System.DateTime)">
            <summary>
            获取月份的最后一天
            </summary>
            <param name="date">日期</param>
            <returns>月份的最后一天</returns>
        </member>
        <member name="M:LicenseManager.Utils.DateTimeHelper.GetFirstDayOfYear(System.DateTime)">
            <summary>
            获取年份的第一天
            </summary>
            <param name="date">日期</param>
            <returns>年份的第一天</returns>
        </member>
        <member name="M:LicenseManager.Utils.DateTimeHelper.GetLastDayOfYear(System.DateTime)">
            <summary>
            获取年份的最后一天
            </summary>
            <param name="date">日期</param>
            <returns>年份的最后一天</returns>
        </member>
        <member name="M:LicenseManager.Utils.DateTimeHelper.GetRelativeTimeString(System.DateTime,System.Nullable{System.DateTime})">
            <summary>
            获取相对时间描述
            </summary>
            <param name="dateTime">日期时间</param>
            <param name="baseTime">基准时间（默认为当前时间）</param>
            <returns>相对时间描述</returns>
        </member>
        <member name="M:LicenseManager.Utils.DateTimeHelper.GetDurationString(System.TimeSpan)">
            <summary>
            获取时间段描述
            </summary>
            <param name="timeSpan">时间段</param>
            <returns>时间段描述</returns>
        </member>
        <member name="M:LicenseManager.Utils.DateTimeHelper.ToUnixTimestamp(System.DateTime)">
            <summary>
            转换为Unix时间戳
            </summary>
            <param name="dateTime">日期时间</param>
            <returns>Unix时间戳</returns>
        </member>
        <member name="M:LicenseManager.Utils.DateTimeHelper.FromUnixTimestamp(System.Int64)">
            <summary>
            从Unix时间戳转换为日期时间
            </summary>
            <param name="unixTimestamp">Unix时间戳</param>
            <returns>日期时间</returns>
        </member>
        <member name="M:LicenseManager.Utils.DateTimeHelper.IsInRange(System.DateTime,System.DateTime,System.DateTime)">
            <summary>
            检查日期是否在指定范围内
            </summary>
            <param name="date">要检查的日期</param>
            <param name="startDate">开始日期</param>
            <param name="endDate">结束日期</param>
            <returns>是否在范围内</returns>
        </member>
        <member name="M:LicenseManager.Utils.DateTimeHelper.GetAge(System.DateTime,System.Nullable{System.DateTime})">
            <summary>
            获取年龄
            </summary>
            <param name="birthDate">出生日期</param>
            <param name="referenceDate">参考日期（默认为当前日期）</param>
            <returns>年龄</returns>
        </member>
        <member name="M:LicenseManager.Utils.DateTimeHelper.GetQuarter(System.DateTime)">
            <summary>
            获取季度
            </summary>
            <param name="date">日期</param>
            <returns>季度（1-4）</returns>
        </member>
        <member name="M:LicenseManager.Utils.DateTimeHelper.GetWeekOfYear(System.DateTime)">
            <summary>
            获取周数（一年中的第几周）
            </summary>
            <param name="date">日期</param>
            <returns>周数</returns>
        </member>
        <member name="M:LicenseManager.Utils.DateTimeHelper.IsLeapYear(System.Int32)">
            <summary>
            检查是否为闰年
            </summary>
            <param name="year">年份</param>
            <returns>是否为闰年</returns>
        </member>
        <member name="M:LicenseManager.Utils.DateTimeHelper.GetDaysInMonth(System.Int32,System.Int32)">
            <summary>
            获取月份的天数
            </summary>
            <param name="year">年份</param>
            <param name="month">月份</param>
            <returns>天数</returns>
        </member>
        <member name="M:LicenseManager.Utils.DateTimeHelper.RoundToInterval(System.DateTime,System.TimeSpan)">
            <summary>
            舍入到最近的时间间隔
            </summary>
            <param name="dateTime">日期时间</param>
            <param name="interval">时间间隔</param>
            <returns>舍入后的日期时间</returns>
        </member>
        <member name="M:LicenseManager.Utils.DateTimeHelper.ToFileNameSafeString(System.DateTime)">
            <summary>
            获取文件名安全的日期时间字符串
            </summary>
            <param name="dateTime">日期时间</param>
            <returns>文件名安全的字符串</returns>
        </member>
        <member name="M:LicenseManager.Utils.DateTimeHelper.GetLicenseTimeInfo(System.DateTime)">
            <summary>
            计算许可证剩余时间信息
            </summary>
            <param name="expiryDate">过期日期</param>
            <returns>剩余时间信息</returns>
        </member>
        <member name="M:LicenseManager.Utils.DateTimeHelper.GetExpiryStatus(System.TimeSpan)">
            <summary>
            获取过期状态
            </summary>
            <param name="timeRemaining">剩余时间</param>
            <returns>过期状态</returns>
        </member>
        <member name="T:LicenseManager.Utils.LicenseTimeInfo">
            <summary>
            许可证时间信息
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.LicenseTimeInfo.ExpiryDate">
            <summary>
            过期日期
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.LicenseTimeInfo.IsExpired">
            <summary>
            是否已过期
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.LicenseTimeInfo.TimeRemaining">
            <summary>
            剩余时间
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.LicenseTimeInfo.DaysRemaining">
            <summary>
            剩余天数
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.LicenseTimeInfo.HoursRemaining">
            <summary>
            剩余小时数
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.LicenseTimeInfo.IsExpiringSoon">
            <summary>
            是否即将过期
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.LicenseTimeInfo.ExpiryStatus">
            <summary>
            过期状态
            </summary>
        </member>
        <member name="T:LicenseManager.Utils.LicenseExpiryStatus">
            <summary>
            许可证过期状态
            </summary>
        </member>
        <member name="T:LicenseManager.Utils.FileHelper">
            <summary>
            文件操作工具类
            提供文件读写、压缩、备份等功能的静态方法
            </summary>
        </member>
        <member name="M:LicenseManager.Utils.FileHelper.EnsureDirectoryExists(System.String)">
            <summary>
            确保目录存在，如果不存在则创建
            </summary>
            <param name="directoryPath">目录路径</param>
        </member>
        <member name="M:LicenseManager.Utils.FileHelper.SafeWriteFileAsync(System.String,System.Byte[])">
            <summary>
            安全地写入文件（先写入临时文件，然后重命名）
            </summary>
            <param name="filePath">文件路径</param>
            <param name="content">文件内容</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:LicenseManager.Utils.FileHelper.SafeWriteTextFileAsync(System.String,System.String,System.Text.Encoding)">
            <summary>
            安全地写入文本文件
            </summary>
            <param name="filePath">文件路径</param>
            <param name="content">文本内容</param>
            <param name="encoding">编码格式</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:LicenseManager.Utils.FileHelper.ReadFileAsync(System.String)">
            <summary>
            读取文件内容
            </summary>
            <param name="filePath">文件路径</param>
            <returns>文件内容</returns>
        </member>
        <member name="M:LicenseManager.Utils.FileHelper.ReadTextFileAsync(System.String,System.Text.Encoding)">
            <summary>
            读取文本文件内容
            </summary>
            <param name="filePath">文件路径</param>
            <param name="encoding">编码格式</param>
            <returns>文本内容</returns>
        </member>
        <member name="M:LicenseManager.Utils.FileHelper.CopyFile(System.String,System.String,System.Boolean)">
            <summary>
            复制文件
            </summary>
            <param name="sourceFilePath">源文件路径</param>
            <param name="destinationFilePath">目标文件路径</param>
            <param name="overwrite">是否覆盖</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:LicenseManager.Utils.FileHelper.MoveFile(System.String,System.String,System.Boolean)">
            <summary>
            移动文件
            </summary>
            <param name="sourceFilePath">源文件路径</param>
            <param name="destinationFilePath">目标文件路径</param>
            <param name="overwrite">是否覆盖</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:LicenseManager.Utils.FileHelper.DeleteFile(System.String)">
            <summary>
            删除文件
            </summary>
            <param name="filePath">文件路径</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:LicenseManager.Utils.FileHelper.GetFileInfo(System.String)">
            <summary>
            获取文件信息
            </summary>
            <param name="filePath">文件路径</param>
            <returns>文件信息</returns>
        </member>
        <member name="M:LicenseManager.Utils.FileHelper.CompressFileAsync(System.String,System.String)">
            <summary>
            压缩文件
            </summary>
            <param name="sourceFilePath">源文件路径</param>
            <param name="compressedFilePath">压缩文件路径</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:LicenseManager.Utils.FileHelper.DecompressFileAsync(System.String,System.String)">
            <summary>
            解压缩文件
            </summary>
            <param name="compressedFilePath">压缩文件路径</param>
            <param name="decompressedFilePath">解压文件路径</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:LicenseManager.Utils.FileHelper.BackupFile(System.String,System.String,System.Boolean)">
            <summary>
            备份文件
            </summary>
            <param name="filePath">文件路径</param>
            <param name="backupDirectory">备份目录</param>
            <param name="includeTimestamp">是否包含时间戳</param>
            <returns>备份文件路径</returns>
        </member>
        <member name="M:LicenseManager.Utils.FileHelper.CleanupOldBackups(System.String,System.Int32,System.String)">
            <summary>
            清理旧备份文件
            </summary>
            <param name="backupDirectory">备份目录</param>
            <param name="retentionDays">保留天数</param>
            <param name="filePattern">文件模式</param>
            <returns>删除的文件数量</returns>
        </member>
        <member name="M:LicenseManager.Utils.FileHelper.GetDirectorySize(System.String,System.Boolean)">
            <summary>
            获取目录大小
            </summary>
            <param name="directoryPath">目录路径</param>
            <param name="includeSubdirectories">是否包含子目录</param>
            <returns>目录大小（字节）</returns>
        </member>
        <member name="M:LicenseManager.Utils.FileHelper.SaveObjectAsJsonAsync``1(``0,System.String,System.Text.Json.JsonSerializerOptions)">
            <summary>
            保存对象为JSON文件
            </summary>
            <typeparam name="T">对象类型</typeparam>
            <param name="obj">要保存的对象</param>
            <param name="filePath">文件路径</param>
            <param name="options">JSON序列化选项</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:LicenseManager.Utils.FileHelper.LoadObjectFromJsonAsync``1(System.String,System.Text.Json.JsonSerializerOptions)">
            <summary>
            从JSON文件加载对象
            </summary>
            <typeparam name="T">对象类型</typeparam>
            <param name="filePath">文件路径</param>
            <param name="options">JSON反序列化选项</param>
            <returns>加载的对象</returns>
        </member>
        <member name="M:LicenseManager.Utils.FileHelper.GenerateUniqueFileName(System.String,System.String,System.String)">
            <summary>
            生成唯一文件名
            </summary>
            <param name="directory">目录</param>
            <param name="fileName">文件名</param>
            <param name="extension">扩展名</param>
            <returns>唯一文件名</returns>
        </member>
        <member name="M:LicenseManager.Utils.FileHelper.GetTempFilePath(System.String)">
            <summary>
            获取临时文件路径
            </summary>
            <param name="extension">文件扩展名</param>
            <returns>临时文件路径</returns>
        </member>
        <member name="M:LicenseManager.Utils.FileHelper.ValidateFilePath(System.String)">
            <summary>
            验证文件路径
            </summary>
            <param name="filePath">文件路径</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:LicenseManager.Utils.FileHelper.WatchFile(System.String,System.Action{System.String})">
            <summary>
            监控文件变化
            </summary>
            <param name="filePath">文件路径</param>
            <param name="callback">变化回调</param>
            <returns>文件监控器</returns>
        </member>
        <member name="T:LicenseManager.Utils.FileInfoResult">
            <summary>
            文件信息结果
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.FileInfoResult.FileName">
            <summary>
            文件名
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.FileInfoResult.FilePath">
            <summary>
            文件路径
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.FileInfoResult.FileSize">
            <summary>
            文件大小
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.FileInfoResult.CreatedTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.FileInfoResult.ModifiedTime">
            <summary>
            修改时间
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.FileInfoResult.AccessedTime">
            <summary>
            访问时间
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.FileInfoResult.Extension">
            <summary>
            文件扩展名
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.FileInfoResult.IsReadOnly">
            <summary>
            是否只读
            </summary>
        </member>
        <member name="T:LicenseManager.Utils.FilePathValidationResult">
            <summary>
            文件路径验证结果
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.FilePathValidationResult.IsValid">
            <summary>
            是否有效
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.FilePathValidationResult.FullPath">
            <summary>
            完整路径
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.FilePathValidationResult.Directory">
            <summary>
            目录
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.FilePathValidationResult.FileName">
            <summary>
            文件名
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.FilePathValidationResult.Extension">
            <summary>
            扩展名
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.FilePathValidationResult.ErrorMessage">
            <summary>
            错误消息
            </summary>
        </member>
        <member name="T:LicenseManager.Utils.SystemHelper">
            <summary>
            系统信息工具类
            提供获取系统硬件和软件信息的静态方法
            </summary>
        </member>
        <member name="M:LicenseManager.Utils.SystemHelper.GetProcessorInfoAsync">
            <summary>
            获取处理器信息
            </summary>
            <returns>处理器信息</returns>
        </member>
        <member name="M:LicenseManager.Utils.SystemHelper.GetMotherboardInfoAsync">
            <summary>
            获取主板信息
            </summary>
            <returns>主板信息</returns>
        </member>
        <member name="M:LicenseManager.Utils.SystemHelper.GetDiskInfoAsync">
            <summary>
            获取硬盘信息
            </summary>
            <returns>硬盘信息列表</returns>
        </member>
        <member name="M:LicenseManager.Utils.SystemHelper.GetNetworkAdapterInfoAsync">
            <summary>
            获取网络适配器信息
            </summary>
            <returns>网络适配器信息列表</returns>
        </member>
        <member name="M:LicenseManager.Utils.SystemHelper.GetMemoryInfoAsync">
            <summary>
            获取内存信息
            </summary>
            <returns>内存信息</returns>
        </member>
        <member name="M:LicenseManager.Utils.SystemHelper.GetGraphicsInfoAsync">
            <summary>
            获取显卡信息
            </summary>
            <returns>显卡信息列表</returns>
        </member>
        <member name="M:LicenseManager.Utils.SystemHelper.GetBiosInfoAsync">
            <summary>
            获取BIOS信息
            </summary>
            <returns>BIOS信息</returns>
        </member>
        <member name="M:LicenseManager.Utils.SystemHelper.GetOperatingSystemInfoAsync">
            <summary>
            获取操作系统信息
            </summary>
            <returns>操作系统信息</returns>
        </member>
        <member name="M:LicenseManager.Utils.SystemHelper.DetectVirtualMachineAsync">
            <summary>
            检测虚拟机
            </summary>
            <returns>虚拟机检测结果</returns>
        </member>
        <member name="M:LicenseManager.Utils.SystemHelper.GetSystemUuidAsync">
            <summary>
            获取系统UUID
            </summary>
            <returns>系统UUID</returns>
        </member>
        <member name="M:LicenseManager.Utils.SystemHelper.GetComputerName">
            <summary>
            获取计算机名称
            </summary>
            <returns>计算机名称</returns>
        </member>
        <member name="M:LicenseManager.Utils.SystemHelper.GetUserName">
            <summary>
            获取用户名
            </summary>
            <returns>用户名</returns>
        </member>
        <member name="M:LicenseManager.Utils.SystemHelper.GetTimeZone">
            <summary>
            获取时区信息
            </summary>
            <returns>时区信息</returns>
        </member>
        <member name="M:LicenseManager.Utils.SystemHelper.IsSystemDisk(System.String)">
            <summary>
            检查是否为系统盘
            </summary>
            <param name="deviceId">设备ID</param>
            <returns>是否为系统盘</returns>
        </member>
        <member name="M:LicenseManager.Utils.SystemHelper.GetSystemPerformance">
            <summary>
            获取系统性能信息
            </summary>
            <returns>性能信息</returns>
        </member>
        <member name="T:LicenseManager.Utils.SystemPerformanceInfo">
            <summary>
            系统性能信息
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.SystemPerformanceInfo.CpuUsage">
            <summary>
            CPU使用率（百分比）
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.SystemPerformanceInfo.MemoryUsage">
            <summary>
            内存使用量（字节）
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.SystemPerformanceInfo.AvailableMemory">
            <summary>
            可用内存（字节）
            </summary>
        </member>
        <member name="T:LicenseManager.Utils.ProcessorInfo">
            <summary>
            处理器信息
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.ProcessorInfo.ProcessorId">
            <summary>
            处理器ID
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.ProcessorInfo.Name">
            <summary>
            处理器名称
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.ProcessorInfo.Manufacturer">
            <summary>
            制造商
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.ProcessorInfo.Architecture">
            <summary>
            架构
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.ProcessorInfo.CoreCount">
            <summary>
            核心数
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.ProcessorInfo.ThreadCount">
            <summary>
            线程数
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.ProcessorInfo.BaseFrequency">
            <summary>
            基础频率（MHz）
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.ProcessorInfo.MaxFrequency">
            <summary>
            最大频率（MHz）
            </summary>
        </member>
        <member name="T:LicenseManager.Utils.MotherboardInfo">
            <summary>
            主板信息
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.MotherboardInfo.SerialNumber">
            <summary>
            主板序列号
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.MotherboardInfo.Manufacturer">
            <summary>
            制造商
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.MotherboardInfo.Product">
            <summary>
            产品名称
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.MotherboardInfo.Version">
            <summary>
            版本
            </summary>
        </member>
        <member name="T:LicenseManager.Utils.DiskInfo">
            <summary>
            硬盘信息
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.DiskInfo.SerialNumber">
            <summary>
            硬盘序列号
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.DiskInfo.Model">
            <summary>
            型号
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.DiskInfo.Manufacturer">
            <summary>
            制造商
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.DiskInfo.Capacity">
            <summary>
            容量（字节）
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.DiskInfo.InterfaceType">
            <summary>
            接口类型
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.DiskInfo.IsSystemDisk">
            <summary>
            是否为系统盘
            </summary>
        </member>
        <member name="T:LicenseManager.Utils.NetworkAdapterInfo">
            <summary>
            网络适配器信息
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.NetworkAdapterInfo.MacAddress">
            <summary>
            MAC地址
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.NetworkAdapterInfo.Name">
            <summary>
            适配器名称
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.NetworkAdapterInfo.Description">
            <summary>
            描述
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.NetworkAdapterInfo.IsEnabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.NetworkAdapterInfo.NetworkType">
            <summary>
            网络类型
            </summary>
        </member>
        <member name="T:LicenseManager.Utils.MemoryInfo">
            <summary>
            内存信息
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.MemoryInfo.TotalMemory">
            <summary>
            总内存（字节）
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.MemoryInfo.AvailableMemory">
            <summary>
            可用内存（字节）
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.MemoryInfo.Modules">
            <summary>
            内存条信息
            </summary>
        </member>
        <member name="T:LicenseManager.Utils.MemoryModuleInfo">
            <summary>
            内存条信息
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.MemoryModuleInfo.SerialNumber">
            <summary>
            序列号
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.MemoryModuleInfo.Capacity">
            <summary>
            容量（字节）
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.MemoryModuleInfo.Manufacturer">
            <summary>
            制造商
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.MemoryModuleInfo.Speed">
            <summary>
            速度（MHz）
            </summary>
        </member>
        <member name="T:LicenseManager.Utils.GraphicsInfo">
            <summary>
            显卡信息
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.GraphicsInfo.Name">
            <summary>
            显卡名称
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.GraphicsInfo.Manufacturer">
            <summary>
            制造商
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.GraphicsInfo.VideoMemory">
            <summary>
            显存大小（字节）
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.GraphicsInfo.DriverVersion">
            <summary>
            驱动版本
            </summary>
        </member>
        <member name="T:LicenseManager.Utils.BiosInfo">
            <summary>
            BIOS信息
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.BiosInfo.SerialNumber">
            <summary>
            BIOS序列号
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.BiosInfo.Manufacturer">
            <summary>
            制造商
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.BiosInfo.Version">
            <summary>
            版本
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.BiosInfo.ReleaseDate">
            <summary>
            发布日期
            </summary>
        </member>
        <member name="T:LicenseManager.Utils.OperatingSystemInfo">
            <summary>
            操作系统信息
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.OperatingSystemInfo.Name">
            <summary>
            操作系统名称
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.OperatingSystemInfo.Version">
            <summary>
            版本
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.OperatingSystemInfo.Architecture">
            <summary>
            架构
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.OperatingSystemInfo.InstallDate">
            <summary>
            安装日期
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.OperatingSystemInfo.SerialNumber">
            <summary>
            序列号
            </summary>
        </member>
        <member name="T:LicenseManager.Utils.VirtualMachineDetectionResult">
            <summary>
            虚拟机检测结果
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.VirtualMachineDetectionResult.IsVirtualMachine">
            <summary>
            是否为虚拟机
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.VirtualMachineDetectionResult.VirtualMachineType">
            <summary>
            虚拟机类型
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.VirtualMachineDetectionResult.DetectionMethods">
            <summary>
            检测方法
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.VirtualMachineDetectionResult.Confidence">
            <summary>
            置信度（0.0-1.0）
            </summary>
        </member>
        <member name="T:LicenseManager.Utils.ValidationHelper">
            <summary>
            验证工具类
            提供各种数据验证功能的静态方法
            </summary>
        </member>
        <member name="M:LicenseManager.Utils.ValidationHelper.IsValidEmail(System.String)">
            <summary>
            验证邮箱地址
            </summary>
            <param name="email">邮箱地址</param>
            <returns>是否有效</returns>
        </member>
        <member name="M:LicenseManager.Utils.ValidationHelper.IsValidUrl(System.String)">
            <summary>
            验证URL
            </summary>
            <param name="url">URL地址</param>
            <returns>是否有效</returns>
        </member>
        <member name="M:LicenseManager.Utils.ValidationHelper.IsValidIpAddress(System.String)">
            <summary>
            验证IP地址
            </summary>
            <param name="ipAddress">IP地址</param>
            <returns>是否有效</returns>
        </member>
        <member name="M:LicenseManager.Utils.ValidationHelper.IsValidMacAddress(System.String)">
            <summary>
            验证MAC地址
            </summary>
            <param name="macAddress">MAC地址</param>
            <returns>是否有效</returns>
        </member>
        <member name="M:LicenseManager.Utils.ValidationHelper.IsValidGuid(System.String)">
            <summary>
            验证GUID
            </summary>
            <param name="guid">GUID字符串</param>
            <returns>是否有效</returns>
        </member>
        <member name="M:LicenseManager.Utils.ValidationHelper.IsValidBase64(System.String)">
            <summary>
            验证Base64字符串
            </summary>
            <param name="base64String">Base64字符串</param>
            <returns>是否有效</returns>
        </member>
        <member name="M:LicenseManager.Utils.ValidationHelper.IsValidHexString(System.String)">
            <summary>
            验证十六进制字符串
            </summary>
            <param name="hexString">十六进制字符串</param>
            <returns>是否有效</returns>
        </member>
        <member name="M:LicenseManager.Utils.ValidationHelper.IsValidLicenseKey(System.String)">
            <summary>
            验证许可证密钥格式
            </summary>
            <param name="licenseKey">许可证密钥</param>
            <returns>是否有效</returns>
        </member>
        <member name="M:LicenseManager.Utils.ValidationHelper.IsValidVersion(System.String)">
            <summary>
            验证版本号
            </summary>
            <param name="version">版本号</param>
            <returns>是否有效</returns>
        </member>
        <member name="M:LicenseManager.Utils.ValidationHelper.IsValidFilePath(System.String)">
            <summary>
            验证文件路径
            </summary>
            <param name="filePath">文件路径</param>
            <returns>是否有效</returns>
        </member>
        <member name="M:LicenseManager.Utils.ValidationHelper.IsValidJson(System.String)">
            <summary>
            验证JSON字符串
            </summary>
            <param name="json">JSON字符串</param>
            <returns>是否有效</returns>
        </member>
        <member name="M:LicenseManager.Utils.ValidationHelper.IsValidXml(System.String)">
            <summary>
            验证XML字符串
            </summary>
            <param name="xml">XML字符串</param>
            <returns>是否有效</returns>
        </member>
        <member name="M:LicenseManager.Utils.ValidationHelper.IsValidRegexPattern(System.String)">
            <summary>
            验证正则表达式
            </summary>
            <param name="pattern">正则表达式模式</param>
            <returns>是否有效</returns>
        </member>
        <member name="M:LicenseManager.Utils.ValidationHelper.IsInRange(System.Int32,System.Int32,System.Int32)">
            <summary>
            验证数字范围
            </summary>
            <param name="value">数值</param>
            <param name="min">最小值</param>
            <param name="max">最大值</param>
            <returns>是否在范围内</returns>
        </member>
        <member name="M:LicenseManager.Utils.ValidationHelper.IsInRange(System.Double,System.Double,System.Double)">
            <summary>
            验证数字范围（双精度）
            </summary>
            <param name="value">数值</param>
            <param name="min">最小值</param>
            <param name="max">最大值</param>
            <returns>是否在范围内</returns>
        </member>
        <member name="M:LicenseManager.Utils.ValidationHelper.IsValidLength(System.String,System.Int32,System.Int32)">
            <summary>
            验证字符串长度
            </summary>
            <param name="value">字符串</param>
            <param name="minLength">最小长度</param>
            <param name="maxLength">最大长度</param>
            <returns>是否在长度范围内</returns>
        </member>
        <member name="M:LicenseManager.Utils.ValidationHelper.IsInDateRange(System.DateTime,System.DateTime,System.DateTime)">
            <summary>
            验证日期范围
            </summary>
            <param name="date">日期</param>
            <param name="minDate">最小日期</param>
            <param name="maxDate">最大日期</param>
            <returns>是否在日期范围内</returns>
        </member>
        <member name="M:LicenseManager.Utils.ValidationHelper.ValidateObject(System.Object)">
            <summary>
            验证对象属性
            </summary>
            <param name="obj">要验证的对象</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:LicenseManager.Utils.ValidationHelper.ValidateProperty(System.Object,System.String,System.Object)">
            <summary>
            验证属性值
            </summary>
            <param name="obj">对象</param>
            <param name="propertyName">属性名</param>
            <param name="value">属性值</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:LicenseManager.Utils.ValidationHelper.ValidateRequired(System.Object,System.String)">
            <summary>
            验证必需字段
            </summary>
            <param name="value">字段值</param>
            <param name="fieldName">字段名</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:LicenseManager.Utils.ValidationHelper.ValidateFormat(System.String,System.String,System.String,System.String)">
            <summary>
            验证字符串格式
            </summary>
            <param name="value">字符串值</param>
            <param name="pattern">正则表达式模式</param>
            <param name="fieldName">字段名</param>
            <param name="errorMessage">错误消息</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:LicenseManager.Utils.ValidationHelper.ValidateBatch(System.Func{LicenseManager.Utils.FieldValidationResult}[])">
            <summary>
            批量验证字段
            </summary>
            <param name="validations">验证函数列表</param>
            <returns>批量验证结果</returns>
        </member>
        <member name="M:LicenseManager.Utils.ValidationHelper.IsValidHardwareFingerprint(System.String)">
            <summary>
            验证硬件指纹格式
            </summary>
            <param name="fingerprint">硬件指纹</param>
            <returns>是否有效</returns>
        </member>
        <member name="M:LicenseManager.Utils.ValidationHelper.ValidateLicenseDates(System.DateTime,System.DateTime)">
            <summary>
            验证许可证有效期
            </summary>
            <param name="issueDate">颁发日期</param>
            <param name="expiryDate">过期日期</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:LicenseManager.Utils.ValidationHelper.GetPropertyValue(System.Object,System.String)">
            <summary>
            获取对象属性值
            </summary>
            <param name="obj">对象</param>
            <param name="propertyName">属性名</param>
            <returns>属性值</returns>
        </member>
        <member name="T:LicenseManager.Utils.ObjectValidationResult">
            <summary>
            对象验证结果
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.ObjectValidationResult.IsValid">
            <summary>
            是否有效
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.ObjectValidationResult.ValidationErrors">
            <summary>
            验证错误列表
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.ObjectValidationResult.ErrorCount">
            <summary>
            错误数量
            </summary>
        </member>
        <member name="T:LicenseManager.Utils.PropertyValidationResult">
            <summary>
            属性验证结果
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.PropertyValidationResult.IsValid">
            <summary>
            是否有效
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.PropertyValidationResult.ValidationErrors">
            <summary>
            验证错误列表
            </summary>
        </member>
        <member name="T:LicenseManager.Utils.FieldValidationResult">
            <summary>
            字段验证结果
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.FieldValidationResult.FieldName">
            <summary>
            字段名
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.FieldValidationResult.IsValid">
            <summary>
            是否有效
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.FieldValidationResult.ErrorMessage">
            <summary>
            错误消息
            </summary>
        </member>
        <member name="T:LicenseManager.Utils.BatchValidationResult">
            <summary>
            批量验证结果
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.BatchValidationResult.IsValid">
            <summary>
            是否有效
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.BatchValidationResult.FieldErrors">
            <summary>
            字段错误列表
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.BatchValidationResult.ErrorCount">
            <summary>
            错误数量
            </summary>
        </member>
        <member name="T:LicenseManager.Utils.ValidationError">
            <summary>
            验证错误
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.ValidationError.PropertyName">
            <summary>
            属性名
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.ValidationError.ErrorMessage">
            <summary>
            错误消息
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.ValidationError.AttemptedValue">
            <summary>
            尝试的值
            </summary>
        </member>
        <member name="T:LicenseManager.Utils.LicenseDateValidationResult">
            <summary>
            许可证日期验证结果
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.LicenseDateValidationResult.IsValid">
            <summary>
            是否有效
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.LicenseDateValidationResult.IsExpired">
            <summary>
            是否已过期
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.LicenseDateValidationResult.DaysRemaining">
            <summary>
            剩余天数
            </summary>
        </member>
        <member name="P:LicenseManager.Utils.LicenseDateValidationResult.ErrorMessage">
            <summary>
            错误消息
            </summary>
        </member>
    </members>
</doc>
