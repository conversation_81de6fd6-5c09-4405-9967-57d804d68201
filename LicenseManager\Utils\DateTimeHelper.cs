using System.Globalization;

namespace LicenseManager.Utils;

/// <summary>
/// 日期时间工具类
/// 提供日期时间处理、格式化、计算等功能的静态方法
/// </summary>
public static class DateTimeHelper
{
    /// <summary>
    /// 标准日期格式
    /// </summary>
    public const string StandardDateFormat = "yyyy-MM-dd";

    /// <summary>
    /// 标准日期时间格式
    /// </summary>
    public const string StandardDateTimeFormat = "yyyy-MM-dd HH:mm:ss";

    /// <summary>
    /// ISO 8601日期时间格式
    /// </summary>
    public const string Iso8601Format = "yyyy-MM-ddTHH:mm:ss.fffZ";

    /// <summary>
    /// 文件名安全的日期时间格式
    /// </summary>
    public const string FileNameSafeDateTimeFormat = "yyyyMMdd_HHmmss";

    /// <summary>
    /// 获取当前UTC时间
    /// </summary>
    /// <returns>当前UTC时间</returns>
    public static DateTime UtcNow => DateTime.UtcNow;

    /// <summary>
    /// 获取当前本地时间
    /// </summary>
    /// <returns>当前本地时间</returns>
    public static DateTime Now => DateTime.Now;

    /// <summary>
    /// 获取今天的开始时间（00:00:00）
    /// </summary>
    /// <returns>今天的开始时间</returns>
    public static DateTime StartOfToday => DateTime.Today;

    /// <summary>
    /// 获取今天的结束时间（23:59:59.999）
    /// </summary>
    /// <returns>今天的结束时间</returns>
    public static DateTime EndOfToday => DateTime.Today.AddDays(1).AddMilliseconds(-1);

    /// <summary>
    /// 获取本周的开始时间（周一00:00:00）
    /// </summary>
    /// <returns>本周的开始时间</returns>
    public static DateTime StartOfWeek
    {
        get
        {
            var today = DateTime.Today;
            var daysFromMonday = (int)today.DayOfWeek - (int)DayOfWeek.Monday;
            if (daysFromMonday < 0) daysFromMonday += 7;
            return today.AddDays(-daysFromMonday);
        }
    }

    /// <summary>
    /// 获取本月的开始时间
    /// </summary>
    /// <returns>本月的开始时间</returns>
    public static DateTime StartOfMonth => new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);

    /// <summary>
    /// 获取本年的开始时间
    /// </summary>
    /// <returns>本年的开始时间</returns>
    public static DateTime StartOfYear => new DateTime(DateTime.Now.Year, 1, 1);

    /// <summary>
    /// 格式化日期时间为标准格式
    /// </summary>
    /// <param name="dateTime">日期时间</param>
    /// <param name="format">格式字符串</param>
    /// <returns>格式化后的字符串</returns>
    public static string Format(DateTime dateTime, string format = StandardDateTimeFormat)
    {
        return dateTime.ToString(format, CultureInfo.InvariantCulture);
    }

    /// <summary>
    /// 格式化日期时间为ISO 8601格式
    /// </summary>
    /// <param name="dateTime">日期时间</param>
    /// <returns>ISO 8601格式字符串</returns>
    public static string ToIso8601String(DateTime dateTime)
    {
        return dateTime.ToUniversalTime().ToString(Iso8601Format, CultureInfo.InvariantCulture);
    }

    /// <summary>
    /// 从ISO 8601字符串解析日期时间
    /// </summary>
    /// <param name="iso8601String">ISO 8601格式字符串</param>
    /// <returns>解析后的日期时间</returns>
    public static DateTime? FromIso8601String(string iso8601String)
    {
        if (string.IsNullOrWhiteSpace(iso8601String))
        {
            return null;
        }

        if (DateTime.TryParseExact(iso8601String, Iso8601Format, CultureInfo.InvariantCulture, DateTimeStyles.AssumeUniversal, out var result))
        {
            return result.ToLocalTime();
        }

        return null;
    }

    /// <summary>
    /// 解析日期时间字符串
    /// </summary>
    /// <param name="dateTimeString">日期时间字符串</param>
    /// <param name="format">格式字符串</param>
    /// <returns>解析后的日期时间</returns>
    public static DateTime? ParseDateTime(string dateTimeString, string? format = null)
    {
        if (string.IsNullOrWhiteSpace(dateTimeString))
        {
            return null;
        }

        if (!string.IsNullOrEmpty(format))
        {
            if (DateTime.TryParseExact(dateTimeString, format, CultureInfo.InvariantCulture, DateTimeStyles.None, out var exactResult))
            {
                return exactResult;
            }
        }

        if (DateTime.TryParse(dateTimeString, CultureInfo.InvariantCulture, DateTimeStyles.None, out var result))
        {
            return result;
        }

        return null;
    }

    /// <summary>
    /// 计算两个日期之间的天数差
    /// </summary>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <returns>天数差</returns>
    public static int DaysBetween(DateTime startDate, DateTime endDate)
    {
        return (endDate.Date - startDate.Date).Days;
    }

    /// <summary>
    /// 计算两个日期之间的工作日数量
    /// </summary>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <returns>工作日数量</returns>
    public static int WorkDaysBetween(DateTime startDate, DateTime endDate)
    {
        if (startDate > endDate)
        {
            (startDate, endDate) = (endDate, startDate);
        }

        var workDays = 0;
        var current = startDate.Date;

        while (current <= endDate.Date)
        {
            if (current.DayOfWeek != DayOfWeek.Saturday && current.DayOfWeek != DayOfWeek.Sunday)
            {
                workDays++;
            }
            current = current.AddDays(1);
        }

        return workDays;
    }

    /// <summary>
    /// 添加工作日
    /// </summary>
    /// <param name="startDate">开始日期</param>
    /// <param name="workDays">工作日数量</param>
    /// <returns>添加工作日后的日期</returns>
    public static DateTime AddWorkDays(DateTime startDate, int workDays)
    {
        var current = startDate.Date;
        var daysAdded = 0;

        while (daysAdded < workDays)
        {
            current = current.AddDays(1);
            if (current.DayOfWeek != DayOfWeek.Saturday && current.DayOfWeek != DayOfWeek.Sunday)
            {
                daysAdded++;
            }
        }

        return current;
    }

    /// <summary>
    /// 检查是否为工作日
    /// </summary>
    /// <param name="date">日期</param>
    /// <returns>是否为工作日</returns>
    public static bool IsWorkDay(DateTime date)
    {
        return date.DayOfWeek != DayOfWeek.Saturday && date.DayOfWeek != DayOfWeek.Sunday;
    }

    /// <summary>
    /// 检查是否为周末
    /// </summary>
    /// <param name="date">日期</param>
    /// <returns>是否为周末</returns>
    public static bool IsWeekend(DateTime date)
    {
        return date.DayOfWeek == DayOfWeek.Saturday || date.DayOfWeek == DayOfWeek.Sunday;
    }

    /// <summary>
    /// 获取月份的第一天
    /// </summary>
    /// <param name="date">日期</param>
    /// <returns>月份的第一天</returns>
    public static DateTime GetFirstDayOfMonth(DateTime date)
    {
        return new DateTime(date.Year, date.Month, 1);
    }

    /// <summary>
    /// 获取月份的最后一天
    /// </summary>
    /// <param name="date">日期</param>
    /// <returns>月份的最后一天</returns>
    public static DateTime GetLastDayOfMonth(DateTime date)
    {
        return GetFirstDayOfMonth(date).AddMonths(1).AddDays(-1);
    }

    /// <summary>
    /// 获取年份的第一天
    /// </summary>
    /// <param name="date">日期</param>
    /// <returns>年份的第一天</returns>
    public static DateTime GetFirstDayOfYear(DateTime date)
    {
        return new DateTime(date.Year, 1, 1);
    }

    /// <summary>
    /// 获取年份的最后一天
    /// </summary>
    /// <param name="date">日期</param>
    /// <returns>年份的最后一天</returns>
    public static DateTime GetLastDayOfYear(DateTime date)
    {
        return new DateTime(date.Year, 12, 31);
    }

    /// <summary>
    /// 获取相对时间描述
    /// </summary>
    /// <param name="dateTime">日期时间</param>
    /// <param name="baseTime">基准时间（默认为当前时间）</param>
    /// <returns>相对时间描述</returns>
    public static string GetRelativeTimeString(DateTime dateTime, DateTime? baseTime = null)
    {
        baseTime ??= DateTime.Now;
        var timeSpan = baseTime.Value - dateTime;

        if (timeSpan.TotalSeconds < 60)
        {
            return "刚刚";
        }

        if (timeSpan.TotalMinutes < 60)
        {
            return $"{(int)timeSpan.TotalMinutes}分钟前";
        }

        if (timeSpan.TotalHours < 24)
        {
            return $"{(int)timeSpan.TotalHours}小时前";
        }

        if (timeSpan.TotalDays < 7)
        {
            return $"{(int)timeSpan.TotalDays}天前";
        }

        if (timeSpan.TotalDays < 30)
        {
            return $"{(int)(timeSpan.TotalDays / 7)}周前";
        }

        if (timeSpan.TotalDays < 365)
        {
            return $"{(int)(timeSpan.TotalDays / 30)}个月前";
        }

        return $"{(int)(timeSpan.TotalDays / 365)}年前";
    }

    /// <summary>
    /// 获取时间段描述
    /// </summary>
    /// <param name="timeSpan">时间段</param>
    /// <returns>时间段描述</returns>
    public static string GetDurationString(TimeSpan timeSpan)
    {
        if (timeSpan.TotalSeconds < 60)
        {
            return $"{(int)timeSpan.TotalSeconds}秒";
        }

        if (timeSpan.TotalMinutes < 60)
        {
            return $"{(int)timeSpan.TotalMinutes}分钟";
        }

        if (timeSpan.TotalHours < 24)
        {
            return $"{(int)timeSpan.TotalHours}小时{timeSpan.Minutes}分钟";
        }

        return $"{(int)timeSpan.TotalDays}天{timeSpan.Hours}小时";
    }

    /// <summary>
    /// 转换为Unix时间戳
    /// </summary>
    /// <param name="dateTime">日期时间</param>
    /// <returns>Unix时间戳</returns>
    public static long ToUnixTimestamp(DateTime dateTime)
    {
        return ((DateTimeOffset)dateTime.ToUniversalTime()).ToUnixTimeSeconds();
    }

    /// <summary>
    /// 从Unix时间戳转换为日期时间
    /// </summary>
    /// <param name="unixTimestamp">Unix时间戳</param>
    /// <returns>日期时间</returns>
    public static DateTime FromUnixTimestamp(long unixTimestamp)
    {
        return DateTimeOffset.FromUnixTimeSeconds(unixTimestamp).DateTime.ToLocalTime();
    }

    /// <summary>
    /// 检查日期是否在指定范围内
    /// </summary>
    /// <param name="date">要检查的日期</param>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <returns>是否在范围内</returns>
    public static bool IsInRange(DateTime date, DateTime startDate, DateTime endDate)
    {
        return date >= startDate && date <= endDate;
    }

    /// <summary>
    /// 获取年龄
    /// </summary>
    /// <param name="birthDate">出生日期</param>
    /// <param name="referenceDate">参考日期（默认为当前日期）</param>
    /// <returns>年龄</returns>
    public static int GetAge(DateTime birthDate, DateTime? referenceDate = null)
    {
        referenceDate ??= DateTime.Today;
        var age = referenceDate.Value.Year - birthDate.Year;

        if (referenceDate.Value.DayOfYear < birthDate.DayOfYear)
        {
            age--;
        }

        return age;
    }

    /// <summary>
    /// 获取季度
    /// </summary>
    /// <param name="date">日期</param>
    /// <returns>季度（1-4）</returns>
    public static int GetQuarter(DateTime date)
    {
        return (date.Month - 1) / 3 + 1;
    }

    /// <summary>
    /// 获取周数（一年中的第几周）
    /// </summary>
    /// <param name="date">日期</param>
    /// <returns>周数</returns>
    public static int GetWeekOfYear(DateTime date)
    {
        var calendar = CultureInfo.CurrentCulture.Calendar;
        return calendar.GetWeekOfYear(date, CalendarWeekRule.FirstDay, DayOfWeek.Monday);
    }

    /// <summary>
    /// 检查是否为闰年
    /// </summary>
    /// <param name="year">年份</param>
    /// <returns>是否为闰年</returns>
    public static bool IsLeapYear(int year)
    {
        return DateTime.IsLeapYear(year);
    }

    /// <summary>
    /// 获取月份的天数
    /// </summary>
    /// <param name="year">年份</param>
    /// <param name="month">月份</param>
    /// <returns>天数</returns>
    public static int GetDaysInMonth(int year, int month)
    {
        return DateTime.DaysInMonth(year, month);
    }

    /// <summary>
    /// 舍入到最近的时间间隔
    /// </summary>
    /// <param name="dateTime">日期时间</param>
    /// <param name="interval">时间间隔</param>
    /// <returns>舍入后的日期时间</returns>
    public static DateTime RoundToInterval(DateTime dateTime, TimeSpan interval)
    {
        var ticks = (long)(Math.Round(dateTime.Ticks / (double)interval.Ticks) * interval.Ticks);
        return new DateTime(ticks);
    }

    /// <summary>
    /// 获取文件名安全的日期时间字符串
    /// </summary>
    /// <param name="dateTime">日期时间</param>
    /// <returns>文件名安全的字符串</returns>
    public static string ToFileNameSafeString(DateTime dateTime)
    {
        return dateTime.ToString(FileNameSafeDateTimeFormat, CultureInfo.InvariantCulture);
    }

    /// <summary>
    /// 计算许可证剩余时间信息
    /// </summary>
    /// <param name="expiryDate">过期日期</param>
    /// <returns>剩余时间信息</returns>
    public static LicenseTimeInfo GetLicenseTimeInfo(DateTime expiryDate)
    {
        var now = DateTime.UtcNow;
        var timeRemaining = expiryDate - now;

        return new LicenseTimeInfo
        {
            ExpiryDate = expiryDate,
            IsExpired = timeRemaining.TotalSeconds <= 0,
            TimeRemaining = timeRemaining.TotalSeconds > 0 ? timeRemaining : TimeSpan.Zero,
            DaysRemaining = Math.Max(0, (int)Math.Ceiling(timeRemaining.TotalDays)),
            HoursRemaining = Math.Max(0, (int)Math.Ceiling(timeRemaining.TotalHours)),
            IsExpiringSoon = timeRemaining.TotalDays <= 30 && timeRemaining.TotalDays > 0,
            ExpiryStatus = GetExpiryStatus(timeRemaining)
        };
    }

    /// <summary>
    /// 获取过期状态
    /// </summary>
    /// <param name="timeRemaining">剩余时间</param>
    /// <returns>过期状态</returns>
    private static LicenseExpiryStatus GetExpiryStatus(TimeSpan timeRemaining)
    {
        if (timeRemaining.TotalSeconds <= 0)
            return LicenseExpiryStatus.Expired;

        if (timeRemaining.TotalDays <= 7)
            return LicenseExpiryStatus.ExpiringSoon;

        if (timeRemaining.TotalDays <= 30)
            return LicenseExpiryStatus.ExpiringThisMonth;

        return LicenseExpiryStatus.Valid;
    }
}

/// <summary>
/// 许可证时间信息
/// </summary>
public class LicenseTimeInfo
{
    /// <summary>
    /// 过期日期
    /// </summary>
    public DateTime ExpiryDate { get; set; }

    /// <summary>
    /// 是否已过期
    /// </summary>
    public bool IsExpired { get; set; }

    /// <summary>
    /// 剩余时间
    /// </summary>
    public TimeSpan TimeRemaining { get; set; }

    /// <summary>
    /// 剩余天数
    /// </summary>
    public int DaysRemaining { get; set; }

    /// <summary>
    /// 剩余小时数
    /// </summary>
    public int HoursRemaining { get; set; }

    /// <summary>
    /// 是否即将过期
    /// </summary>
    public bool IsExpiringSoon { get; set; }

    /// <summary>
    /// 过期状态
    /// </summary>
    public LicenseExpiryStatus ExpiryStatus { get; set; }
}

/// <summary>
/// 许可证过期状态
/// </summary>
public enum LicenseExpiryStatus
{
    Valid,
    ExpiringThisMonth,
    ExpiringSoon,
    Expired
}
