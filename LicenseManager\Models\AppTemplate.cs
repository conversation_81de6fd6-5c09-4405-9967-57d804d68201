using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace LicenseManager.Models;

/// <summary>
/// 应用程序模板实体模型
/// 存储不同应用程序的权限配置模板和相关信息
/// </summary>
[Table("AppTemplates")]
public class AppTemplate
{
    /// <summary>
    /// 应用程序模板唯一标识符
    /// </summary>
    [Key]
    public int Id { get; set; }

    /// <summary>
    /// 应用程序名称
    /// </summary>
    [Required]
    [StringLength(200)]
    public string AppName { get; set; } = string.Empty;

    /// <summary>
    /// 应用程序版本
    /// </summary>
    [Required]
    [StringLength(50)]
    public string AppVersion { get; set; } = string.Empty;

    /// <summary>
    /// 应用程序标识符（用于许可证生成）
    /// </summary>
    [Required]
    [StringLength(100)]
    public string AppId { get; set; } = string.Empty;

    /// <summary>
    /// 模板数据（JSON格式存储权限配置、功能列表等）
    /// </summary>
    [Required]
    [StringLength(4000)]
    public string TemplateData { get; set; } = string.Empty;

    /// <summary>
    /// 模板描述
    /// </summary>
    [StringLength(1000)]
    public string? Description { get; set; }

    /// <summary>
    /// 应用程序开发商
    /// </summary>
    [StringLength(200)]
    public string? Developer { get; set; }

    /// <summary>
    /// 应用程序官网
    /// </summary>
    [StringLength(500)]
    [Url]
    public string? Website { get; set; }

    /// <summary>
    /// 联系邮箱
    /// </summary>
    [StringLength(200)]
    [EmailAddress]
    public string? ContactEmail { get; set; }

    /// <summary>
    /// 应用程序类型
    /// </summary>
    [StringLength(100)]
    public string? AppType { get; set; }

    /// <summary>
    /// 支持的操作系统
    /// </summary>
    [StringLength(200)]
    public string? SupportedOS { get; set; } = "Windows";

    /// <summary>
    /// 最低系统要求
    /// </summary>
    [StringLength(500)]
    public string? MinimumRequirements { get; set; }

    /// <summary>
    /// 默认授权类型ID
    /// </summary>
    public int? DefaultAuthorizationTypeId { get; set; }

    /// <summary>
    /// 是否启用硬件绑定
    /// </summary>
    [Required]
    public bool EnableHardwareBinding { get; set; } = true;

    /// <summary>
    /// 是否启用网络验证
    /// </summary>
    [Required]
    public bool EnableNetworkValidation { get; set; } = false;

    /// <summary>
    /// 许可证文件扩展名
    /// </summary>
    [StringLength(20)]
    public string? LicenseFileExtension { get; set; } = ".lic";

    /// <summary>
    /// 加密算法类型
    /// </summary>
    [StringLength(50)]
    public string? EncryptionType { get; set; } = "RSA";

    /// <summary>
    /// 公钥（用于许可证验证）
    /// </summary>
    [StringLength(2000)]
    public string? PublicKey { get; set; }

    /// <summary>
    /// 私钥（用于许可证生成，加密存储）
    /// </summary>
    [StringLength(4000)]
    public string? PrivateKey { get; set; }

    /// <summary>
    /// 模板版本号
    /// </summary>
    [Required]
    public int TemplateVersion { get; set; } = 1;

    /// <summary>
    /// 是否为默认模板
    /// </summary>
    [Required]
    public bool IsDefault { get; set; } = false;

    /// <summary>
    /// 是否启用
    /// </summary>
    [Required]
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 排序顺序
    /// </summary>
    [Required]
    public int SortOrder { get; set; } = 0;

    /// <summary>
    /// 标签（用于分类和搜索）
    /// </summary>
    [StringLength(500)]
    public string? Tags { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    [Required]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 最后更新时间
    /// </summary>
    [Required]
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    // 导航属性
    /// <summary>
    /// 默认授权类型
    /// </summary>
    [ForeignKey(nameof(DefaultAuthorizationTypeId))]
    public virtual AuthorizationType? DefaultAuthorizationType { get; set; }

    /// <summary>
    /// 计算属性：应用程序完整名称
    /// </summary>
    [NotMapped]
    public string FullName => $"{AppName} v{AppVersion}";

    /// <summary>
    /// 计算属性：模板数据大小（字节）
    /// </summary>
    [NotMapped]
    public int TemplateDataSize => System.Text.Encoding.UTF8.GetByteCount(TemplateData ?? string.Empty);

    /// <summary>
    /// 计算属性：是否配置了加密密钥
    /// </summary>
    [NotMapped]
    public bool HasEncryptionKeys => !string.IsNullOrEmpty(PublicKey) && !string.IsNullOrEmpty(PrivateKey);

    /// <summary>
    /// 计算属性：状态描述
    /// </summary>
    [NotMapped]
    public string StatusDescription
    {
        get
        {
            if (!IsEnabled) return "已禁用";
            if (!HasEncryptionKeys) return "未配置密钥";
            if (IsDefault) return "默认模板";
            return "正常";
        }
    }

    /// <summary>
    /// 计算属性：标签列表
    /// </summary>
    [NotMapped]
    public string[] TagList
    {
        get
        {
            if (string.IsNullOrEmpty(Tags)) return Array.Empty<string>();
            return Tags.Split(',', StringSplitOptions.RemoveEmptyEntries)
                      .Select(tag => tag.Trim())
                      .Where(tag => !string.IsNullOrEmpty(tag))
                      .ToArray();
        }
    }
}
