using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Security.Cryptography;
using System.Text;

namespace LicenseManager.Models;

/// <summary>
/// 硬件指纹实体模型
/// 存储用于硬件绑定的设备指纹信息
/// </summary>
[Table("HardwareFingerprints")]
public class HardwareFingerprint
{
    /// <summary>
    /// 硬件指纹唯一标识符
    /// </summary>
    [Key]
    public int Id { get; set; }

    /// <summary>
    /// 处理器ID
    /// </summary>
    [StringLength(200)]
    public string? ProcessorId { get; set; }

    /// <summary>
    /// 主板ID
    /// </summary>
    [StringLength(200)]
    public string? MotherboardId { get; set; }

    /// <summary>
    /// 硬盘序列号
    /// </summary>
    [StringLength(200)]
    public string? DiskId { get; set; }

    /// <summary>
    /// MAC地址
    /// </summary>
    [StringLength(200)]
    public string? MacAddress { get; set; }

    /// <summary>
    /// BIOS序列号
    /// </summary>
    [StringLength(200)]
    public string? BiosSerialNumber { get; set; }

    /// <summary>
    /// 系统UUID
    /// </summary>
    [StringLength(200)]
    public string? SystemUuid { get; set; }

    /// <summary>
    /// 内存信息
    /// </summary>
    [StringLength(500)]
    public string? MemoryInfo { get; set; }

    /// <summary>
    /// 显卡信息
    /// </summary>
    [StringLength(500)]
    public string? GraphicsInfo { get; set; }

    /// <summary>
    /// 操作系统信息
    /// </summary>
    [StringLength(200)]
    public string? OperatingSystem { get; set; }

    /// <summary>
    /// 计算机名称
    /// </summary>
    [StringLength(100)]
    public string? ComputerName { get; set; }

    /// <summary>
    /// 用户名
    /// </summary>
    [StringLength(100)]
    public string? UserName { get; set; }

    /// <summary>
    /// 时区信息
    /// </summary>
    [StringLength(100)]
    public string? TimeZone { get; set; }

    /// <summary>
    /// 综合指纹哈希值（基于多个硬件信息计算）
    /// </summary>
    [Required]
    [StringLength(128)]
    public string FingerprintHash { get; set; } = string.Empty;

    /// <summary>
    /// 指纹生成算法版本
    /// </summary>
    [Required]
    public int AlgorithmVersion { get; set; } = 1;

    /// <summary>
    /// 指纹强度等级（1-5，数字越大越严格）
    /// </summary>
    [Required]
    public int StrengthLevel { get; set; } = 3;

    /// <summary>
    /// 是否允许部分匹配
    /// </summary>
    [Required]
    public bool AllowPartialMatch { get; set; } = false;

    /// <summary>
    /// 部分匹配阈值（0.0-1.0，表示匹配度）
    /// </summary>
    [Column(TypeName = "decimal(3,2)")]
    public decimal PartialMatchThreshold { get; set; } = 0.8m;

    /// <summary>
    /// 指纹描述
    /// </summary>
    [StringLength(500)]
    public string? Description { get; set; }

    /// <summary>
    /// 设备类型
    /// </summary>
    [StringLength(50)]
    public string? DeviceType { get; set; } = "Desktop";

    /// <summary>
    /// 是否为虚拟机
    /// </summary>
    public bool? IsVirtualMachine { get; set; }

    /// <summary>
    /// 虚拟机类型
    /// </summary>
    [StringLength(100)]
    public string? VirtualMachineType { get; set; }

    /// <summary>
    /// 最后验证时间
    /// </summary>
    public DateTime? LastValidatedAt { get; set; }

    /// <summary>
    /// 验证次数
    /// </summary>
    [Required]
    public int ValidationCount { get; set; } = 0;

    /// <summary>
    /// 是否启用
    /// </summary>
    [Required]
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 创建时间
    /// </summary>
    [Required]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 最后更新时间
    /// </summary>
    [Required]
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 计算属性：硬件信息完整性
    /// </summary>
    [NotMapped]
    public double CompletenessScore
    {
        get
        {
            var fields = new[]
            {
                ProcessorId, MotherboardId, DiskId, MacAddress,
                BiosSerialNumber, SystemUuid, MemoryInfo, GraphicsInfo
            };
            
            var filledFields = fields.Count(f => !string.IsNullOrEmpty(f));
            return (double)filledFields / fields.Length;
        }
    }

    /// <summary>
    /// 计算属性：指纹状态描述
    /// </summary>
    [NotMapped]
    public string StatusDescription
    {
        get
        {
            if (!IsEnabled) return "已禁用";
            if (CompletenessScore < 0.5) return "信息不完整";
            if (IsVirtualMachine == true) return "虚拟机";
            return "正常";
        }
    }

    /// <summary>
    /// 生成硬件指纹哈希值
    /// </summary>
    /// <returns>指纹哈希值</returns>
    public string GenerateFingerprintHash()
    {
        var components = new List<string>();

        // 根据强度等级选择不同的硬件组件
        switch (StrengthLevel)
        {
            case 1: // 最低强度：仅使用处理器ID
                if (!string.IsNullOrEmpty(ProcessorId)) components.Add(ProcessorId);
                break;
            
            case 2: // 低强度：处理器ID + 主板ID
                if (!string.IsNullOrEmpty(ProcessorId)) components.Add(ProcessorId);
                if (!string.IsNullOrEmpty(MotherboardId)) components.Add(MotherboardId);
                break;
            
            case 3: // 中等强度：处理器ID + 主板ID + 硬盘ID
                if (!string.IsNullOrEmpty(ProcessorId)) components.Add(ProcessorId);
                if (!string.IsNullOrEmpty(MotherboardId)) components.Add(MotherboardId);
                if (!string.IsNullOrEmpty(DiskId)) components.Add(DiskId);
                break;
            
            case 4: // 高强度：添加MAC地址和BIOS序列号
                if (!string.IsNullOrEmpty(ProcessorId)) components.Add(ProcessorId);
                if (!string.IsNullOrEmpty(MotherboardId)) components.Add(MotherboardId);
                if (!string.IsNullOrEmpty(DiskId)) components.Add(DiskId);
                if (!string.IsNullOrEmpty(MacAddress)) components.Add(MacAddress);
                if (!string.IsNullOrEmpty(BiosSerialNumber)) components.Add(BiosSerialNumber);
                break;
            
            case 5: // 最高强度：使用所有可用信息
                if (!string.IsNullOrEmpty(ProcessorId)) components.Add(ProcessorId);
                if (!string.IsNullOrEmpty(MotherboardId)) components.Add(MotherboardId);
                if (!string.IsNullOrEmpty(DiskId)) components.Add(DiskId);
                if (!string.IsNullOrEmpty(MacAddress)) components.Add(MacAddress);
                if (!string.IsNullOrEmpty(BiosSerialNumber)) components.Add(BiosSerialNumber);
                if (!string.IsNullOrEmpty(SystemUuid)) components.Add(SystemUuid);
                if (!string.IsNullOrEmpty(MemoryInfo)) components.Add(MemoryInfo);
                break;
        }

        if (components.Count == 0)
        {
            throw new InvalidOperationException("没有足够的硬件信息来生成指纹");
        }

        // 组合所有组件并生成哈希
        var combined = string.Join("|", components.OrderBy(c => c));
        var bytes = Encoding.UTF8.GetBytes(combined);
        var hash = SHA256.HashData(bytes);
        
        FingerprintHash = Convert.ToHexString(hash);
        return FingerprintHash;
    }

    /// <summary>
    /// 验证指纹匹配度
    /// </summary>
    /// <param name="otherFingerprint">要比较的指纹</param>
    /// <returns>匹配度（0.0-1.0）</returns>
    public decimal CalculateMatchScore(HardwareFingerprint otherFingerprint)
    {
        if (otherFingerprint == null) return 0m;

        // 如果哈希值完全匹配
        if (FingerprintHash.Equals(otherFingerprint.FingerprintHash, StringComparison.OrdinalIgnoreCase))
        {
            return 1.0m;
        }

        // 如果不允许部分匹配
        if (!AllowPartialMatch) return 0m;

        // 计算各个组件的匹配度
        var matches = 0;
        var total = 0;

        var comparisons = new[]
        {
            (ProcessorId, otherFingerprint.ProcessorId),
            (MotherboardId, otherFingerprint.MotherboardId),
            (DiskId, otherFingerprint.DiskId),
            (MacAddress, otherFingerprint.MacAddress),
            (BiosSerialNumber, otherFingerprint.BiosSerialNumber),
            (SystemUuid, otherFingerprint.SystemUuid)
        };

        foreach (var (current, other) in comparisons)
        {
            if (!string.IsNullOrEmpty(current) && !string.IsNullOrEmpty(other))
            {
                total++;
                if (current.Equals(other, StringComparison.OrdinalIgnoreCase))
                {
                    matches++;
                }
            }
        }

        return total > 0 ? (decimal)matches / total : 0m;
    }
}
