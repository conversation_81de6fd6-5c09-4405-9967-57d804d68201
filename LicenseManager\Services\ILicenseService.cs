using LicenseManager.Models;
using LicenseManager.Data.Repositories;

namespace LicenseManager.Services;

/// <summary>
/// 许可证服务接口
/// 提供许可证生成、验证、管理等核心业务功能
/// </summary>
public interface ILicenseService
{
    /// <summary>
    /// 生成许可证
    /// </summary>
    /// <param name="request">许可证生成请求</param>
    /// <returns>生成的许可证</returns>
    Task<LicenseGenerationResult> GenerateLicenseAsync(LicenseGenerationRequest request);

    /// <summary>
    /// 批量生成许可证
    /// </summary>
    /// <param name="requests">许可证生成请求列表</param>
    /// <returns>生成结果列表</returns>
    Task<IEnumerable<LicenseGenerationResult>> GenerateLicensesBatchAsync(IEnumerable<LicenseGenerationRequest> requests);

    /// <summary>
    /// 验证许可证
    /// </summary>
    /// <param name="licenseKey">许可证密钥</param>
    /// <param name="hardwareFingerprint">硬件指纹（可选）</param>
    /// <param name="appId">应用程序ID（可选）</param>
    /// <returns>验证结果</returns>
    Task<LicenseValidationResult> ValidateLicenseAsync(string licenseKey, string? hardwareFingerprint = null, string? appId = null);

    /// <summary>
    /// 激活许可证
    /// </summary>
    /// <param name="licenseKey">许可证密钥</param>
    /// <param name="hardwareFingerprint">硬件指纹</param>
    /// <returns>激活结果</returns>
    Task<LicenseActivationResult> ActivateLicenseAsync(string licenseKey, string hardwareFingerprint);

    /// <summary>
    /// 停用许可证
    /// </summary>
    /// <param name="licenseId">许可证ID</param>
    /// <param name="reason">停用原因</param>
    /// <returns>是否成功</returns>
    Task<bool> DeactivateLicenseAsync(int licenseId, string? reason = null);

    /// <summary>
    /// 延长许可证有效期
    /// </summary>
    /// <param name="licenseId">许可证ID</param>
    /// <param name="additionalDays">延长天数</param>
    /// <returns>是否成功</returns>
    Task<bool> ExtendLicenseAsync(int licenseId, int additionalDays);

    /// <summary>
    /// 更新许可证信息
    /// </summary>
    /// <param name="licenseId">许可证ID</param>
    /// <param name="updateRequest">更新请求</param>
    /// <returns>更新后的许可证</returns>
    Task<License?> UpdateLicenseAsync(int licenseId, LicenseUpdateRequest updateRequest);

    /// <summary>
    /// 删除许可证
    /// </summary>
    /// <param name="licenseId">许可证ID</param>
    /// <returns>是否成功</returns>
    Task<bool> DeleteLicenseAsync(int licenseId);

    /// <summary>
    /// 获取许可证详细信息
    /// </summary>
    /// <param name="licenseId">许可证ID</param>
    /// <returns>许可证详细信息</returns>
    Task<LicenseDetailInfo?> GetLicenseDetailAsync(int licenseId);

    /// <summary>
    /// 搜索许可证
    /// </summary>
    /// <param name="searchRequest">搜索请求</param>
    /// <returns>搜索结果</returns>
    Task<PagedResult<License>> SearchLicensesAsync(LicenseSearchRequest searchRequest);

    /// <summary>
    /// 获取许可证统计信息
    /// </summary>
    /// <returns>统计信息</returns>
    Task<LicenseStatistics> GetLicenseStatisticsAsync();

    /// <summary>
    /// 导出许可证文件
    /// </summary>
    /// <param name="licenseId">许可证ID</param>
    /// <param name="format">导出格式</param>
    /// <returns>许可证文件内容</returns>
    Task<LicenseFileResult> ExportLicenseFileAsync(int licenseId, LicenseFileFormat format = LicenseFileFormat.Binary);

    /// <summary>
    /// 导入许可证文件
    /// </summary>
    /// <param name="fileContent">文件内容</param>
    /// <param name="fileName">文件名</param>
    /// <returns>导入结果</returns>
    Task<LicenseImportResult> ImportLicenseFileAsync(byte[] fileContent, string fileName);

    /// <summary>
    /// 检查许可证冲突
    /// </summary>
    /// <param name="request">许可证生成请求</param>
    /// <returns>冲突检查结果</returns>
    Task<LicenseConflictResult> CheckLicenseConflictAsync(LicenseGenerationRequest request);

    /// <summary>
    /// 获取许可证使用历史
    /// </summary>
    /// <param name="licenseId">许可证ID</param>
    /// <param name="pageIndex">页索引</param>
    /// <param name="pageSize">页大小</param>
    /// <returns>使用历史</returns>
    Task<PagedResult<LicenseUsageHistory>> GetLicenseUsageHistoryAsync(int licenseId, int pageIndex = 0, int pageSize = 20);
}

/// <summary>
/// 许可证生成请求
/// </summary>
public class LicenseGenerationRequest
{
    /// <summary>
    /// 应用程序ID
    /// </summary>
    public string AppId { get; set; } = string.Empty;

    /// <summary>
    /// 应用程序名称
    /// </summary>
    public string AppName { get; set; } = string.Empty;

    /// <summary>
    /// 授权类型ID
    /// </summary>
    public int AuthorizationTypeId { get; set; }

    /// <summary>
    /// 客户名称
    /// </summary>
    public string? CustomerName { get; set; }

    /// <summary>
    /// 客户邮箱
    /// </summary>
    public string? CustomerEmail { get; set; }

    /// <summary>
    /// 有效期天数（如果不指定则使用授权类型的默认值）
    /// </summary>
    public int? ValidityDays { get; set; }

    /// <summary>
    /// 最大用户数量
    /// </summary>
    public int? MaxUsers { get; set; }

    /// <summary>
    /// 硬件指纹（用于硬件绑定）
    /// </summary>
    public string? HardwareFingerprint { get; set; }

    /// <summary>
    /// 功能特性列表
    /// </summary>
    public string[]? Features { get; set; }

    /// <summary>
    /// 自定义属性
    /// </summary>
    public Dictionary<string, string>? CustomProperties { get; set; }

    /// <summary>
    /// 备注信息
    /// </summary>
    public string? Notes { get; set; }
}

/// <summary>
/// 许可证生成结果
/// </summary>
public class LicenseGenerationResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 生成的许可证
    /// </summary>
    public License? License { get; set; }

    /// <summary>
    /// 许可证密钥
    /// </summary>
    public string? LicenseKey { get; set; }

    /// <summary>
    /// 许可证文件内容
    /// </summary>
    public byte[]? LicenseFileContent { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 错误代码
    /// </summary>
    public string? ErrorCode { get; set; }
}

/// <summary>
/// 许可证激活结果
/// </summary>
public class LicenseActivationResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 许可证信息
    /// </summary>
    public License? License { get; set; }

    /// <summary>
    /// 激活令牌
    /// </summary>
    public string? ActivationToken { get; set; }

    /// <summary>
    /// 消息
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 错误代码
    /// </summary>
    public string? ErrorCode { get; set; }
}

/// <summary>
/// 许可证更新请求
/// </summary>
public class LicenseUpdateRequest
{
    /// <summary>
    /// 客户名称
    /// </summary>
    public string? CustomerName { get; set; }

    /// <summary>
    /// 客户邮箱
    /// </summary>
    public string? CustomerEmail { get; set; }

    /// <summary>
    /// 最大用户数量
    /// </summary>
    public int? MaxUsers { get; set; }

    /// <summary>
    /// 功能特性
    /// </summary>
    public string? Features { get; set; }

    /// <summary>
    /// 备注信息
    /// </summary>
    public string? Notes { get; set; }

    /// <summary>
    /// 是否激活
    /// </summary>
    public bool? IsActive { get; set; }
}

/// <summary>
/// 许可证搜索请求
/// </summary>
public class LicenseSearchRequest
{
    /// <summary>
    /// 搜索关键词
    /// </summary>
    public string? SearchTerm { get; set; }

    /// <summary>
    /// 应用程序ID
    /// </summary>
    public string? AppId { get; set; }

    /// <summary>
    /// 授权类型ID
    /// </summary>
    public int? AuthorizationTypeId { get; set; }

    /// <summary>
    /// 是否激活
    /// </summary>
    public bool? IsActive { get; set; }

    /// <summary>
    /// 是否过期
    /// </summary>
    public bool? IsExpired { get; set; }

    /// <summary>
    /// 开始日期
    /// </summary>
    public DateTime? StartDate { get; set; }

    /// <summary>
    /// 结束日期
    /// </summary>
    public DateTime? EndDate { get; set; }

    /// <summary>
    /// 页索引
    /// </summary>
    public int PageIndex { get; set; } = 0;

    /// <summary>
    /// 页大小
    /// </summary>
    public int PageSize { get; set; } = 20;

    /// <summary>
    /// 排序字段
    /// </summary>
    public string? SortBy { get; set; }

    /// <summary>
    /// 是否升序
    /// </summary>
    public bool Ascending { get; set; } = true;
}

/// <summary>
/// 许可证详细信息
/// </summary>
public class LicenseDetailInfo
{
    /// <summary>
    /// 许可证基本信息
    /// </summary>
    public License License { get; set; } = null!;

    /// <summary>
    /// 授权类型信息
    /// </summary>
    public AuthorizationType? AuthorizationType { get; set; }

    /// <summary>
    /// 扩展属性列表
    /// </summary>
    public IEnumerable<LicenseInfo> LicenseInfos { get; set; } = new List<LicenseInfo>();

    /// <summary>
    /// 硬件指纹信息
    /// </summary>
    public HardwareFingerprint? HardwareFingerprint { get; set; }

    /// <summary>
    /// 使用统计
    /// </summary>
    public LicenseUsageStatistics? UsageStatistics { get; set; }
}

/// <summary>
/// 许可证使用统计
/// </summary>
public class LicenseUsageStatistics
{
    /// <summary>
    /// 总验证次数
    /// </summary>
    public int TotalValidations { get; set; }

    /// <summary>
    /// 成功验证次数
    /// </summary>
    public int SuccessfulValidations { get; set; }

    /// <summary>
    /// 失败验证次数
    /// </summary>
    public int FailedValidations { get; set; }

    /// <summary>
    /// 最后验证时间
    /// </summary>
    public DateTime? LastValidationTime { get; set; }

    /// <summary>
    /// 最后验证IP
    /// </summary>
    public string? LastValidationIp { get; set; }

    /// <summary>
    /// 激活次数
    /// </summary>
    public int ActivationCount { get; set; }

    /// <summary>
    /// 最后激活时间
    /// </summary>
    public DateTime? LastActivationTime { get; set; }
}

/// <summary>
/// 许可证文件结果
/// </summary>
public class LicenseFileResult
{
    /// <summary>
    /// 文件内容
    /// </summary>
    public byte[] Content { get; set; } = Array.Empty<byte>();

    /// <summary>
    /// 文件名
    /// </summary>
    public string FileName { get; set; } = string.Empty;

    /// <summary>
    /// 内容类型
    /// </summary>
    public string ContentType { get; set; } = "application/octet-stream";

    /// <summary>
    /// 文件大小
    /// </summary>
    public long FileSize => Content.Length;
}

/// <summary>
/// 许可证导入结果
/// </summary>
public class LicenseImportResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 导入的许可证
    /// </summary>
    public License? License { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 警告消息
    /// </summary>
    public string[]? Warnings { get; set; }
}

/// <summary>
/// 许可证冲突结果
/// </summary>
public class LicenseConflictResult
{
    /// <summary>
    /// 是否有冲突
    /// </summary>
    public bool HasConflict { get; set; }

    /// <summary>
    /// 冲突类型
    /// </summary>
    public LicenseConflictType ConflictType { get; set; }

    /// <summary>
    /// 冲突描述
    /// </summary>
    public string? ConflictDescription { get; set; }

    /// <summary>
    /// 冲突的许可证列表
    /// </summary>
    public IEnumerable<License>? ConflictingLicenses { get; set; }
}

/// <summary>
/// 许可证冲突类型
/// </summary>
public enum LicenseConflictType
{
    None,
    DuplicateHardwareFingerprint,
    DuplicateCustomerEmail,
    ExceedsMaxLicenses,
    InvalidAuthorizationType
}

/// <summary>
/// 许可证文件格式
/// </summary>
public enum LicenseFileFormat
{
    Binary,
    Json,
    Xml,
    Encrypted
}
