using System.Security.Cryptography;
using System.Text;
using System.Text.Json;

namespace LicenseManager.Utils;

/// <summary>
/// 加密工具类
/// 提供各种加密、解密、哈希等功能的静态方法
/// </summary>
public static class CryptoHelper
{
    /// <summary>
    /// 生成RSA密钥对
    /// </summary>
    /// <param name="keySize">密钥长度（位）</param>
    /// <returns>RSA密钥对</returns>
    public static (string PublicKey, string PrivateKey) GenerateRsaKeyPair(int keySize = 2048)
    {
        using var rsa = RSA.Create(keySize);
        var publicKey = Convert.ToBase64String(rsa.ExportRSAPublicKey());
        var privateKey = Convert.ToBase64String(rsa.ExportRSAPrivateKey());
        return (publicKey, privateKey);
    }

    /// <summary>
    /// RSA加密
    /// </summary>
    /// <param name="data">要加密的数据</param>
    /// <param name="publicKey">公钥（Base64格式）</param>
    /// <returns>加密后的数据</returns>
    public static byte[] RsaEncrypt(byte[] data, string publicKey)
    {
        using var rsa = RSA.Create();
        rsa.ImportRSAPublicKey(Convert.FromBase64String(publicKey), out _);
        return rsa.Encrypt(data, RSAEncryptionPadding.OaepSHA256);
    }

    /// <summary>
    /// RSA解密
    /// </summary>
    /// <param name="encryptedData">加密的数据</param>
    /// <param name="privateKey">私钥（Base64格式）</param>
    /// <returns>解密后的数据</returns>
    public static byte[] RsaDecrypt(byte[] encryptedData, string privateKey)
    {
        using var rsa = RSA.Create();
        rsa.ImportRSAPrivateKey(Convert.FromBase64String(privateKey), out _);
        return rsa.Decrypt(encryptedData, RSAEncryptionPadding.OaepSHA256);
    }

    /// <summary>
    /// RSA签名
    /// </summary>
    /// <param name="data">要签名的数据</param>
    /// <param name="privateKey">私钥（Base64格式）</param>
    /// <returns>签名</returns>
    public static byte[] RsaSign(byte[] data, string privateKey)
    {
        using var rsa = RSA.Create();
        rsa.ImportRSAPrivateKey(Convert.FromBase64String(privateKey), out _);
        return rsa.SignData(data, HashAlgorithmName.SHA256, RSASignaturePadding.Pkcs1);
    }

    /// <summary>
    /// RSA验证签名
    /// </summary>
    /// <param name="data">原始数据</param>
    /// <param name="signature">签名</param>
    /// <param name="publicKey">公钥（Base64格式）</param>
    /// <returns>验证结果</returns>
    public static bool RsaVerifySignature(byte[] data, byte[] signature, string publicKey)
    {
        try
        {
            using var rsa = RSA.Create();
            rsa.ImportRSAPublicKey(Convert.FromBase64String(publicKey), out _);
            return rsa.VerifyData(data, signature, HashAlgorithmName.SHA256, RSASignaturePadding.Pkcs1);
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 生成AES密钥和IV
    /// </summary>
    /// <param name="keySize">密钥长度（位）</param>
    /// <returns>密钥和IV</returns>
    public static (byte[] Key, byte[] IV) GenerateAesKey(int keySize = 256)
    {
        using var aes = Aes.Create();
        aes.KeySize = keySize;
        aes.GenerateKey();
        aes.GenerateIV();
        return (aes.Key, aes.IV);
    }

    /// <summary>
    /// AES加密
    /// </summary>
    /// <param name="data">要加密的数据</param>
    /// <param name="key">密钥</param>
    /// <param name="iv">初始化向量</param>
    /// <returns>加密后的数据</returns>
    public static byte[] AesEncrypt(byte[] data, byte[] key, byte[] iv)
    {
        using var aes = Aes.Create();
        aes.Key = key;
        aes.IV = iv;
        
        using var encryptor = aes.CreateEncryptor();
        using var msEncrypt = new MemoryStream();
        using var csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write);
        
        csEncrypt.Write(data, 0, data.Length);
        csEncrypt.FlushFinalBlock();
        
        return msEncrypt.ToArray();
    }

    /// <summary>
    /// AES解密
    /// </summary>
    /// <param name="encryptedData">加密的数据</param>
    /// <param name="key">密钥</param>
    /// <param name="iv">初始化向量</param>
    /// <returns>解密后的数据</returns>
    public static byte[] AesDecrypt(byte[] encryptedData, byte[] key, byte[] iv)
    {
        using var aes = Aes.Create();
        aes.Key = key;
        aes.IV = iv;
        
        using var decryptor = aes.CreateDecryptor();
        using var msDecrypt = new MemoryStream(encryptedData);
        using var csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read);
        using var msResult = new MemoryStream();
        
        csDecrypt.CopyTo(msResult);
        return msResult.ToArray();
    }

    /// <summary>
    /// 计算SHA256哈希
    /// </summary>
    /// <param name="data">要计算哈希的数据</param>
    /// <returns>哈希值</returns>
    public static byte[] ComputeSha256Hash(byte[] data)
    {
        return SHA256.HashData(data);
    }

    /// <summary>
    /// 计算SHA256哈希（字符串）
    /// </summary>
    /// <param name="input">输入字符串</param>
    /// <returns>哈希值（十六进制字符串）</returns>
    public static string ComputeSha256Hash(string input)
    {
        var data = Encoding.UTF8.GetBytes(input);
        var hash = SHA256.HashData(data);
        return Convert.ToHexString(hash);
    }

    /// <summary>
    /// 计算MD5哈希
    /// </summary>
    /// <param name="data">要计算哈希的数据</param>
    /// <returns>哈希值</returns>
    public static byte[] ComputeMd5Hash(byte[] data)
    {
        return MD5.HashData(data);
    }

    /// <summary>
    /// 计算MD5哈希（字符串）
    /// </summary>
    /// <param name="input">输入字符串</param>
    /// <returns>哈希值（十六进制字符串）</returns>
    public static string ComputeMd5Hash(string input)
    {
        var data = Encoding.UTF8.GetBytes(input);
        var hash = MD5.HashData(data);
        return Convert.ToHexString(hash);
    }

    /// <summary>
    /// 生成随机字节
    /// </summary>
    /// <param name="length">字节长度</param>
    /// <returns>随机字节数组</returns>
    public static byte[] GenerateRandomBytes(int length)
    {
        var bytes = new byte[length];
        RandomNumberGenerator.Fill(bytes);
        return bytes;
    }

    /// <summary>
    /// 生成随机字符串
    /// </summary>
    /// <param name="length">字符串长度</param>
    /// <param name="includeSpecialChars">是否包含特殊字符</param>
    /// <returns>随机字符串</returns>
    public static string GenerateRandomString(int length, bool includeSpecialChars = false)
    {
        const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        const string specialChars = "!@#$%^&*()_+-=[]{}|;:,.<>?";
        
        var availableChars = includeSpecialChars ? chars + specialChars : chars;
        var random = new Random();
        
        return new string(Enumerable.Repeat(availableChars, length)
            .Select(s => s[random.Next(s.Length)]).ToArray());
    }

    /// <summary>
    /// Base64编码
    /// </summary>
    /// <param name="data">要编码的数据</param>
    /// <returns>Base64字符串</returns>
    public static string ToBase64String(byte[] data)
    {
        return Convert.ToBase64String(data);
    }

    /// <summary>
    /// Base64解码
    /// </summary>
    /// <param name="base64String">Base64字符串</param>
    /// <returns>解码后的数据</returns>
    public static byte[] FromBase64String(string base64String)
    {
        return Convert.FromBase64String(base64String);
    }

    /// <summary>
    /// 十六进制编码
    /// </summary>
    /// <param name="data">要编码的数据</param>
    /// <returns>十六进制字符串</returns>
    public static string ToHexString(byte[] data)
    {
        return Convert.ToHexString(data);
    }

    /// <summary>
    /// 十六进制解码
    /// </summary>
    /// <param name="hexString">十六进制字符串</param>
    /// <returns>解码后的数据</returns>
    public static byte[] FromHexString(string hexString)
    {
        return Convert.FromHexString(hexString);
    }

    /// <summary>
    /// 密码哈希（使用PBKDF2）
    /// </summary>
    /// <param name="password">密码</param>
    /// <param name="salt">盐值</param>
    /// <param name="iterations">迭代次数</param>
    /// <param name="hashLength">哈希长度</param>
    /// <returns>密码哈希</returns>
    public static byte[] HashPassword(string password, byte[] salt, int iterations = 10000, int hashLength = 32)
    {
        using var pbkdf2 = new Rfc2898DeriveBytes(password, salt, iterations, HashAlgorithmName.SHA256);
        return pbkdf2.GetBytes(hashLength);
    }

    /// <summary>
    /// 验证密码
    /// </summary>
    /// <param name="password">密码</param>
    /// <param name="hash">存储的哈希</param>
    /// <param name="salt">盐值</param>
    /// <param name="iterations">迭代次数</param>
    /// <returns>验证结果</returns>
    public static bool VerifyPassword(string password, byte[] hash, byte[] salt, int iterations = 10000)
    {
        var computedHash = HashPassword(password, salt, iterations, hash.Length);
        return CryptographicOperations.FixedTimeEquals(hash, computedHash);
    }

    /// <summary>
    /// 生成许可证密钥
    /// </summary>
    /// <param name="prefix">前缀</param>
    /// <param name="length">密钥长度</param>
    /// <returns>许可证密钥</returns>
    public static string GenerateLicenseKey(string prefix = "LIC", int length = 25)
    {
        var keyLength = length - prefix.Length - 1; // 减去前缀和分隔符的长度
        var randomPart = GenerateRandomString(keyLength).ToUpper();
        
        // 每5个字符插入一个分隔符
        var formattedKey = string.Join("-", 
            Enumerable.Range(0, (randomPart.Length + 4) / 5)
                     .Select(i => randomPart.Substring(i * 5, Math.Min(5, randomPart.Length - i * 5))));
        
        return $"{prefix}-{formattedKey}";
    }

    /// <summary>
    /// 加密对象为JSON
    /// </summary>
    /// <typeparam name="T">对象类型</typeparam>
    /// <param name="obj">要加密的对象</param>
    /// <param name="publicKey">公钥</param>
    /// <returns>加密后的Base64字符串</returns>
    public static string EncryptObjectToJson<T>(T obj, string publicKey)
    {
        var json = JsonSerializer.Serialize(obj);
        var data = Encoding.UTF8.GetBytes(json);
        var encryptedData = RsaEncrypt(data, publicKey);
        return ToBase64String(encryptedData);
    }

    /// <summary>
    /// 从加密的JSON解密对象
    /// </summary>
    /// <typeparam name="T">对象类型</typeparam>
    /// <param name="encryptedJson">加密的JSON字符串</param>
    /// <param name="privateKey">私钥</param>
    /// <returns>解密后的对象</returns>
    public static T? DecryptObjectFromJson<T>(string encryptedJson, string privateKey)
    {
        try
        {
            var encryptedData = FromBase64String(encryptedJson);
            var decryptedData = RsaDecrypt(encryptedData, privateKey);
            var json = Encoding.UTF8.GetString(decryptedData);
            return JsonSerializer.Deserialize<T>(json);
        }
        catch
        {
            return default(T);
        }
    }

    /// <summary>
    /// 计算文件哈希
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>文件SHA256哈希值</returns>
    public static async Task<string> ComputeFileHashAsync(string filePath)
    {
        using var stream = File.OpenRead(filePath);
        var hash = await SHA256.HashDataAsync(stream);
        return Convert.ToHexString(hash);
    }

    /// <summary>
    /// 安全比较两个字节数组
    /// </summary>
    /// <param name="a">数组A</param>
    /// <param name="b">数组B</param>
    /// <returns>是否相等</returns>
    public static bool SecureEquals(byte[] a, byte[] b)
    {
        return CryptographicOperations.FixedTimeEquals(a, b);
    }

    /// <summary>
    /// 生成HMAC签名
    /// </summary>
    /// <param name="data">要签名的数据</param>
    /// <param name="key">密钥</param>
    /// <returns>HMAC签名</returns>
    public static byte[] ComputeHmacSha256(byte[] data, byte[] key)
    {
        using var hmac = new HMACSHA256(key);
        return hmac.ComputeHash(data);
    }

    /// <summary>
    /// 验证HMAC签名
    /// </summary>
    /// <param name="data">原始数据</param>
    /// <param name="signature">签名</param>
    /// <param name="key">密钥</param>
    /// <returns>验证结果</returns>
    public static bool VerifyHmacSha256(byte[] data, byte[] signature, byte[] key)
    {
        var computedSignature = ComputeHmacSha256(data, key);
        return SecureEquals(signature, computedSignature);
    }
}
