using LicenseManager.Models;

namespace LicenseManager.Services;

/// <summary>
/// 硬件指纹服务接口
/// 提供硬件信息收集、指纹生成、匹配验证等功能
/// </summary>
public interface IHardwareFingerprintService
{
    /// <summary>
    /// 获取当前系统的硬件指纹
    /// </summary>
    /// <param name="strengthLevel">指纹强度等级（1-5）</param>
    /// <returns>硬件指纹对象</returns>
    Task<HardwareFingerprint> GetCurrentHardwareFingerprintAsync(int strengthLevel = 3);

    /// <summary>
    /// 生成硬件指纹哈希值
    /// </summary>
    /// <param name="fingerprint">硬件指纹对象</param>
    /// <returns>指纹哈希值</returns>
    string GenerateFingerprintHash(HardwareFingerprint fingerprint);

    /// <summary>
    /// 比较两个硬件指纹的匹配度
    /// </summary>
    /// <param name="fingerprint1">指纹1</param>
    /// <param name="fingerprint2">指纹2</param>
    /// <returns>匹配度（0.0-1.0）</returns>
    decimal CalculateMatchScore(HardwareFingerprint fingerprint1, HardwareFingerprint fingerprint2);

    /// <summary>
    /// 验证硬件指纹是否匹配
    /// </summary>
    /// <param name="storedFingerprint">存储的指纹</param>
    /// <param name="currentFingerprint">当前指纹</param>
    /// <param name="threshold">匹配阈值</param>
    /// <returns>匹配结果</returns>
    FingerprintMatchResult ValidateFingerprint(HardwareFingerprint storedFingerprint, HardwareFingerprint currentFingerprint, decimal threshold = 0.8m);

    /// <summary>
    /// 获取处理器信息
    /// </summary>
    /// <returns>处理器信息</returns>
    Task<ProcessorInfo> GetProcessorInfoAsync();

    /// <summary>
    /// 获取主板信息
    /// </summary>
    /// <returns>主板信息</returns>
    Task<MotherboardInfo> GetMotherboardInfoAsync();

    /// <summary>
    /// 获取硬盘信息
    /// </summary>
    /// <returns>硬盘信息列表</returns>
    Task<IEnumerable<DiskInfo>> GetDiskInfoAsync();

    /// <summary>
    /// 获取网络适配器信息
    /// </summary>
    /// <returns>网络适配器信息列表</returns>
    Task<IEnumerable<NetworkAdapterInfo>> GetNetworkAdapterInfoAsync();

    /// <summary>
    /// 获取内存信息
    /// </summary>
    /// <returns>内存信息</returns>
    Task<MemoryInfo> GetMemoryInfoAsync();

    /// <summary>
    /// 获取显卡信息
    /// </summary>
    /// <returns>显卡信息列表</returns>
    Task<IEnumerable<GraphicsInfo>> GetGraphicsInfoAsync();

    /// <summary>
    /// 获取BIOS信息
    /// </summary>
    /// <returns>BIOS信息</returns>
    Task<BiosInfo> GetBiosInfoAsync();

    /// <summary>
    /// 获取操作系统信息
    /// </summary>
    /// <returns>操作系统信息</returns>
    Task<OperatingSystemInfo> GetOperatingSystemInfoAsync();

    /// <summary>
    /// 检测是否为虚拟机
    /// </summary>
    /// <returns>虚拟机检测结果</returns>
    Task<VirtualMachineDetectionResult> DetectVirtualMachineAsync();

    /// <summary>
    /// 获取系统唯一标识符
    /// </summary>
    /// <returns>系统UUID</returns>
    Task<string> GetSystemUuidAsync();

    /// <summary>
    /// 获取硬件变更历史
    /// </summary>
    /// <param name="fingerprintId">指纹ID</param>
    /// <returns>变更历史</returns>
    Task<IEnumerable<HardwareChangeHistory>> GetHardwareChangeHistoryAsync(int fingerprintId);

    /// <summary>
    /// 记录硬件变更
    /// </summary>
    /// <param name="oldFingerprint">旧指纹</param>
    /// <param name="newFingerprint">新指纹</param>
    /// <returns>是否成功</returns>
    Task<bool> RecordHardwareChangeAsync(HardwareFingerprint oldFingerprint, HardwareFingerprint newFingerprint);

    /// <summary>
    /// 生成硬件指纹报告
    /// </summary>
    /// <param name="fingerprint">硬件指纹</param>
    /// <returns>指纹报告</returns>
    HardwareFingerprintReport GenerateFingerprintReport(HardwareFingerprint fingerprint);

    /// <summary>
    /// 导出硬件指纹
    /// </summary>
    /// <param name="fingerprint">硬件指纹</param>
    /// <param name="format">导出格式</param>
    /// <returns>导出结果</returns>
    Task<FingerprintExportResult> ExportFingerprintAsync(HardwareFingerprint fingerprint, FingerprintExportFormat format);

    /// <summary>
    /// 导入硬件指纹
    /// </summary>
    /// <param name="data">指纹数据</param>
    /// <param name="format">数据格式</param>
    /// <returns>导入结果</returns>
    Task<FingerprintImportResult> ImportFingerprintAsync(byte[] data, FingerprintExportFormat format);

    /// <summary>
    /// 优化硬件指纹配置
    /// </summary>
    /// <param name="fingerprint">硬件指纹</param>
    /// <param name="optimizationOptions">优化选项</param>
    /// <returns>优化后的指纹</returns>
    HardwareFingerprint OptimizeFingerprint(HardwareFingerprint fingerprint, FingerprintOptimizationOptions optimizationOptions);
}

/// <summary>
/// 指纹匹配结果
/// </summary>
public class FingerprintMatchResult
{
    /// <summary>
    /// 是否匹配
    /// </summary>
    public bool IsMatch { get; set; }

    /// <summary>
    /// 匹配度
    /// </summary>
    public decimal MatchScore { get; set; }

    /// <summary>
    /// 匹配的组件
    /// </summary>
    public string[] MatchedComponents { get; set; } = Array.Empty<string>();

    /// <summary>
    /// 不匹配的组件
    /// </summary>
    public string[] MismatchedComponents { get; set; } = Array.Empty<string>();

    /// <summary>
    /// 匹配详情
    /// </summary>
    public Dictionary<string, ComponentMatchInfo> ComponentMatches { get; set; } = new();

    /// <summary>
    /// 验证时间
    /// </summary>
    public DateTime ValidatedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 组件匹配信息
/// </summary>
public class ComponentMatchInfo
{
    /// <summary>
    /// 组件名称
    /// </summary>
    public string ComponentName { get; set; } = string.Empty;

    /// <summary>
    /// 是否匹配
    /// </summary>
    public bool IsMatch { get; set; }

    /// <summary>
    /// 存储的值
    /// </summary>
    public string? StoredValue { get; set; }

    /// <summary>
    /// 当前值
    /// </summary>
    public string? CurrentValue { get; set; }

    /// <summary>
    /// 相似度
    /// </summary>
    public decimal Similarity { get; set; }
}

/// <summary>
/// 处理器信息
/// </summary>
public class ProcessorInfo
{
    /// <summary>
    /// 处理器ID
    /// </summary>
    public string ProcessorId { get; set; } = string.Empty;

    /// <summary>
    /// 处理器名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 制造商
    /// </summary>
    public string Manufacturer { get; set; } = string.Empty;

    /// <summary>
    /// 架构
    /// </summary>
    public string Architecture { get; set; } = string.Empty;

    /// <summary>
    /// 核心数
    /// </summary>
    public int CoreCount { get; set; }

    /// <summary>
    /// 线程数
    /// </summary>
    public int ThreadCount { get; set; }

    /// <summary>
    /// 基础频率（MHz）
    /// </summary>
    public int BaseFrequency { get; set; }

    /// <summary>
    /// 最大频率（MHz）
    /// </summary>
    public int MaxFrequency { get; set; }
}

/// <summary>
/// 主板信息
/// </summary>
public class MotherboardInfo
{
    /// <summary>
    /// 主板序列号
    /// </summary>
    public string SerialNumber { get; set; } = string.Empty;

    /// <summary>
    /// 制造商
    /// </summary>
    public string Manufacturer { get; set; } = string.Empty;

    /// <summary>
    /// 产品名称
    /// </summary>
    public string Product { get; set; } = string.Empty;

    /// <summary>
    /// 版本
    /// </summary>
    public string Version { get; set; } = string.Empty;
}

/// <summary>
/// 硬盘信息
/// </summary>
public class DiskInfo
{
    /// <summary>
    /// 硬盘序列号
    /// </summary>
    public string SerialNumber { get; set; } = string.Empty;

    /// <summary>
    /// 型号
    /// </summary>
    public string Model { get; set; } = string.Empty;

    /// <summary>
    /// 制造商
    /// </summary>
    public string Manufacturer { get; set; } = string.Empty;

    /// <summary>
    /// 容量（字节）
    /// </summary>
    public long Capacity { get; set; }

    /// <summary>
    /// 接口类型
    /// </summary>
    public string InterfaceType { get; set; } = string.Empty;

    /// <summary>
    /// 是否为系统盘
    /// </summary>
    public bool IsSystemDisk { get; set; }
}

/// <summary>
/// 网络适配器信息
/// </summary>
public class NetworkAdapterInfo
{
    /// <summary>
    /// MAC地址
    /// </summary>
    public string MacAddress { get; set; } = string.Empty;

    /// <summary>
    /// 适配器名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; }

    /// <summary>
    /// 网络类型
    /// </summary>
    public string NetworkType { get; set; } = string.Empty;
}

/// <summary>
/// 内存信息
/// </summary>
public class MemoryInfo
{
    /// <summary>
    /// 总内存（字节）
    /// </summary>
    public long TotalMemory { get; set; }

    /// <summary>
    /// 可用内存（字节）
    /// </summary>
    public long AvailableMemory { get; set; }

    /// <summary>
    /// 内存条信息
    /// </summary>
    public IEnumerable<MemoryModuleInfo> Modules { get; set; } = new List<MemoryModuleInfo>();
}

/// <summary>
/// 内存条信息
/// </summary>
public class MemoryModuleInfo
{
    /// <summary>
    /// 序列号
    /// </summary>
    public string SerialNumber { get; set; } = string.Empty;

    /// <summary>
    /// 容量（字节）
    /// </summary>
    public long Capacity { get; set; }

    /// <summary>
    /// 制造商
    /// </summary>
    public string Manufacturer { get; set; } = string.Empty;

    /// <summary>
    /// 速度（MHz）
    /// </summary>
    public int Speed { get; set; }
}

/// <summary>
/// 显卡信息
/// </summary>
public class GraphicsInfo
{
    /// <summary>
    /// 显卡名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 制造商
    /// </summary>
    public string Manufacturer { get; set; } = string.Empty;

    /// <summary>
    /// 显存大小（字节）
    /// </summary>
    public long VideoMemory { get; set; }

    /// <summary>
    /// 驱动版本
    /// </summary>
    public string DriverVersion { get; set; } = string.Empty;
}

/// <summary>
/// BIOS信息
/// </summary>
public class BiosInfo
{
    /// <summary>
    /// BIOS序列号
    /// </summary>
    public string SerialNumber { get; set; } = string.Empty;

    /// <summary>
    /// 制造商
    /// </summary>
    public string Manufacturer { get; set; } = string.Empty;

    /// <summary>
    /// 版本
    /// </summary>
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// 发布日期
    /// </summary>
    public DateTime? ReleaseDate { get; set; }
}

/// <summary>
/// 操作系统信息
/// </summary>
public class OperatingSystemInfo
{
    /// <summary>
    /// 操作系统名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 版本
    /// </summary>
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// 架构
    /// </summary>
    public string Architecture { get; set; } = string.Empty;

    /// <summary>
    /// 安装日期
    /// </summary>
    public DateTime? InstallDate { get; set; }

    /// <summary>
    /// 序列号
    /// </summary>
    public string SerialNumber { get; set; } = string.Empty;
}

/// <summary>
/// 虚拟机检测结果
/// </summary>
public class VirtualMachineDetectionResult
{
    /// <summary>
    /// 是否为虚拟机
    /// </summary>
    public bool IsVirtualMachine { get; set; }

    /// <summary>
    /// 虚拟机类型
    /// </summary>
    public string? VirtualMachineType { get; set; }

    /// <summary>
    /// 检测方法
    /// </summary>
    public string[] DetectionMethods { get; set; } = Array.Empty<string>();

    /// <summary>
    /// 置信度（0.0-1.0）
    /// </summary>
    public decimal Confidence { get; set; }
}

/// <summary>
/// 硬件变更历史
/// </summary>
public class HardwareChangeHistory
{
    /// <summary>
    /// 变更ID
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// 指纹ID
    /// </summary>
    public int FingerprintId { get; set; }

    /// <summary>
    /// 变更类型
    /// </summary>
    public string ChangeType { get; set; } = string.Empty;

    /// <summary>
    /// 变更描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 旧值
    /// </summary>
    public string? OldValue { get; set; }

    /// <summary>
    /// 新值
    /// </summary>
    public string? NewValue { get; set; }

    /// <summary>
    /// 变更时间
    /// </summary>
    public DateTime ChangeTime { get; set; }
}

/// <summary>
/// 硬件指纹报告
/// </summary>
public class HardwareFingerprintReport
{
    /// <summary>
    /// 指纹摘要
    /// </summary>
    public string Summary { get; set; } = string.Empty;

    /// <summary>
    /// 完整性评分
    /// </summary>
    public double CompletenessScore { get; set; }

    /// <summary>
    /// 唯一性评分
    /// </summary>
    public double UniquenessScore { get; set; }

    /// <summary>
    /// 稳定性评分
    /// </summary>
    public double StabilityScore { get; set; }

    /// <summary>
    /// 组件详情
    /// </summary>
    public Dictionary<string, ComponentReport> ComponentReports { get; set; } = new();

    /// <summary>
    /// 建议
    /// </summary>
    public string[] Recommendations { get; set; } = Array.Empty<string>();
}

/// <summary>
/// 组件报告
/// </summary>
public class ComponentReport
{
    /// <summary>
    /// 组件名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 状态
    /// </summary>
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// 值
    /// </summary>
    public string? Value { get; set; }

    /// <summary>
    /// 可靠性评分
    /// </summary>
    public double ReliabilityScore { get; set; }
}

/// <summary>
/// 指纹导出结果
/// </summary>
public class FingerprintExportResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 导出数据
    /// </summary>
    public byte[]? Data { get; set; }

    /// <summary>
    /// 文件名
    /// </summary>
    public string? FileName { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// 指纹导入结果
/// </summary>
public class FingerprintImportResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 导入的指纹
    /// </summary>
    public HardwareFingerprint? Fingerprint { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 警告消息
    /// </summary>
    public string[]? Warnings { get; set; }
}

/// <summary>
/// 指纹优化选项
/// </summary>
public class FingerprintOptimizationOptions
{
    /// <summary>
    /// 目标强度等级
    /// </summary>
    public int TargetStrengthLevel { get; set; } = 3;

    /// <summary>
    /// 是否优化性能
    /// </summary>
    public bool OptimizePerformance { get; set; } = true;

    /// <summary>
    /// 是否优化稳定性
    /// </summary>
    public bool OptimizeStability { get; set; } = true;

    /// <summary>
    /// 排除的组件
    /// </summary>
    public string[]? ExcludedComponents { get; set; }
}

/// <summary>
/// 指纹导出格式
/// </summary>
public enum FingerprintExportFormat
{
    Json,
    Xml,
    Binary,
    Csv
}
