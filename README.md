# 通用软件授权管理系统（注册机）

## 项目概括
本项目旨在开发一个基于 WPF 和 .NET 9 的桌面应用程序，用于通用软件授权管理和许可证生成。该系统采用 MVVM 架构模式，提供直观的用户界面和流畅的用户体验，支持为不同应用程序生成、验证和管理数字许可证文件。

## 技术选型
- 开发框架: WPF (Windows Presentation Foundation)
- .NET 版本: .NET 9.0
- 架构模式: MVVM (Model-View-ViewModel)
- UI框架: 原生WPF控件（不使用第三方UI库）
- 数据访问: Entity Framework Core
- 数据存储: SQLite 数据库
- 加密算法: RSA + AES 混合加密
- 依赖注入: Microsoft.Extensions.DependencyInjection
- 消息传递: CommunityToolkit.Mvvm
- 版本控制: Git
- 其他工具: NUnit (测试), AutoMapper (对象映射), Ser<PERSON>g (日志), Newtonsoft.Json (JSON处理)

## WPF 项目结构 / 模块划分
- `/Views/`: XAML 视图文件
  - `MainWindow.xaml`: 主窗口
  - `LicenseGenerationView.xaml`: License生成界面
  - `LicenseManagementView.xaml`: License管理界面
  - `LicenseValidationView.xaml`: License验证界面
  - `AuthorizationTypeView.xaml`: 授权类型管理界面
  - `AppTemplateView.xaml`: 应用模板管理界面
  - `BatchGenerationView.xaml`: 批量生成界面
  - `SettingsView.xaml`: 系统设置界面
- `/ViewModels/`: 视图模型类
  - `MainViewModel.cs`: 主窗口视图模型
  - `LicenseGenerationViewModel.cs`: License生成视图模型
  - `LicenseManagementViewModel.cs`: License管理视图模型
  - `LicenseValidationViewModel.cs`: License验证视图模型
  - `AuthorizationTypeViewModel.cs`: 授权类型管理视图模型
  - `AppTemplateViewModel.cs`: 应用模板管理视图模型
  - `BatchGenerationViewModel.cs`: 批量生成视图模型
  - `SettingsViewModel.cs`: 系统设置视图模型
- `/Models/`: 数据模型类
  - `License.cs`: 许可证实体模型
  - `AuthorizationType.cs`: 授权类型实体模型
  - `AppTemplate.cs`: 应用程序模板实体模型
  - `LicenseInfo.cs`: 许可证信息实体模型
  - `HardwareFingerprint.cs`: 硬件指纹实体模型
- `/Services/`: 业务服务层
  - `LicenseService.cs`: 许可证业务逻辑服务
  - `EncryptionService.cs`: 加密解密服务
  - `ValidationService.cs`: 验证服务
  - `HardwareFingerprintService.cs`: 硬件指纹服务
  - `DataService.cs`: 数据访问服务
- `/Converters/`: 值转换器
  - `BoolToVisibilityConverter.cs`: 布尔值到可见性转换器
  - `DateTimeToStringConverter.cs`: 日期时间转换器
  - `LicenseStatusConverter.cs`: 许可证状态转换器
- `/Controls/`: 自定义用户控件
  - `LicenseInfoCard.xaml`: 许可证信息卡片控件
  - `ProgressIndicator.xaml`: 进度指示器控件
- `/Resources/`: 资源文件
  - `/Styles/`: 样式文件
    - `ButtonStyles.xaml`: 按钮样式
    - `TextBoxStyles.xaml`: 文本框样式
    - `DataGridStyles.xaml`: 数据网格样式
  - `/Templates/`: 模板文件
    - `ControlTemplates.xaml`: 控件模板
  - `/Images/`: 图片资源
- `/Data/`: 数据访问层
  - `LicenseDbContext.cs`: 数据库上下文
  - `Repositories/`: 仓储模式实现
- `/Utils/`: 工具类和辅助函数
  - `CryptoHelper.cs`: 加密工具类
  - `FileHelper.cs`: 文件操作工具类
  - `ValidationHelper.cs`: 验证工具类
- `App.xaml`: 应用程序定义
- `MainWindow.xaml`: 主窗口
- `LicenseManager.csproj`: 项目文件

## 核心功能模块 / 界面详解
- `License生成模块`: 提供单个许可证生成功能，支持自定义应用ID、授权类型、有效期、硬件绑定等参数，生成加密的许可证文件。
- `License验证模块`: 提供许可证文件验证功能，检查许可证的有效性、过期时间、硬件绑定等信息。
- `授权类型管理模块`: 管理不同的授权类型配置（试用版、标准版、专业版、企业版等），支持增删改查操作。
- `应用权限模板管理模块`: 管理不同应用程序的权限配置模板，支持导入、导出和编辑模板文件。
- `批量License生成模块`: 支持批量生成多个许可证文件，可基于Excel或CSV文件导入批量数据。
- `License信息管理模块`: 提供已生成许可证的查看、编辑、删除功能，支持数据筛选、排序和分页显示。
- `系统设置模块`: 提供应用程序配置管理，包括加密密钥设置、数据库配置、界面主题等。

## 数据模型设计
- License: { Id (int, PK), AppId (string), AppName (string), AuthorizationTypeId (int, FK), LicenseKey (string), HardwareFingerprint (string), IssueDate (DateTime), ExpiryDate (DateTime), IsActive (bool), CreatedAt (DateTime), UpdatedAt (DateTime) }
- AuthorizationType: { Id (int, PK), Name (string), Description (string), MaxUsers (int), Features (string), ValidityDays (int), CreatedAt (DateTime), UpdatedAt (DateTime) }
- AppTemplate: { Id (int, PK), AppName (string), AppVersion (string), TemplateData (string), Description (string), CreatedAt (DateTime), UpdatedAt (DateTime) }
- LicenseInfo: { Id (int, PK), LicenseId (int, FK), PropertyName (string), PropertyValue (string), CreatedAt (DateTime) }
- HardwareFingerprint: { Id (int, PK), ProcessorId (string), MotherboardId (string), DiskId (string), MacAddress (string), FingerprintHash (string), CreatedAt (DateTime) }

## WPF 架构设计
- **View层**: 负责用户界面展示，使用XAML定义界面布局和样式，包括主窗口、各功能模块界面、对话框等
- **ViewModel层**: 作为View和Model之间的桥梁，处理界面逻辑和数据绑定，实现INotifyPropertyChanged接口，管理命令绑定
- **Model层**: 定义业务数据模型和业务逻辑，包括许可证实体、授权类型、应用模板等数据模型
- **Service层**: 提供数据访问、业务服务等功能，包括许可证服务、加密服务、验证服务等
- **依赖注入**: 使用Microsoft.Extensions.DependencyInjection容器管理对象生命周期和依赖关系

## 界面设计规范
- **主题风格**: 现代简洁的原生WPF风格，使用系统默认主题
- **色彩方案**: 主色调为深蓝色(#2E3440)，辅助色为浅灰色(#F5F5F5)，强调色为橙色(#FF6B35)
- **字体规范**: 标题使用微软雅黑 14pt Bold，正文使用微软雅黑 12pt Regular，按钮使用微软雅黑 11pt Regular
- **控件样式**: 按钮采用圆角设计，文本框带有边框高亮效果，数据网格使用交替行颜色
- **布局原则**: 采用Grid和StackPanel组合布局，确保界面在不同分辨率下的适配性

## 技术实现细节

### 项目创建与初始化 (已完成)

#### 1. 项目创建
- 使用 `dotnet new wpf -n LicenseManager -f net9.0` 创建基础WPF项目
- 目标框架：.NET 9.0-windows
- 项目类型：WinExe (Windows可执行文件)
- 启用功能：WPF、可空引用类型、隐式全局using

#### 2. 目录结构搭建
按照MVVM架构模式创建完整的项目目录结构：
```
LicenseManager/
├── Views/              # XAML视图文件
├── ViewModels/         # 视图模型类
├── Models/             # 数据模型类
├── Services/           # 业务服务层
├── Converters/         # 值转换器
├── Controls/           # 自定义用户控件
├── Resources/          # 资源文件
│   ├── Styles/         # 样式文件
│   ├── Templates/      # 模板文件
│   └── Images/         # 图片资源
├── Data/               # 数据访问层
│   └── Repositories/   # 仓储模式实现
└── Utils/              # 工具类和辅助函数
```

#### 3. NuGet包管理
成功安装以下核心依赖包：
- **Entity Framework Core**: Microsoft.EntityFrameworkCore.Sqlite (9.0.5)
- **MVVM工具包**: CommunityToolkit.Mvvm (8.4.0)
- **依赖注入**: Microsoft.Extensions.Hosting (9.0.5)
- **日志记录**: Serilog (4.3.0), Serilog.Sinks.File (7.0.0), Serilog.Extensions.Hosting (9.0.0)
- **JSON处理**: Newtonsoft.Json (13.0.3)
- **对象映射**: AutoMapper (14.0.0), AutoMapper.Extensions.Microsoft.DependencyInjection (12.0.1)

#### 4. 基础配置文件
- **appsettings.json**: 应用程序配置，包含数据库连接、日志配置、许可证设置、UI配置
- **app.manifest**: 应用程序清单，配置UAC权限、DPI感知、Windows兼容性

#### 5. 项目文件优化
- 添加详细的程序集信息（标题、描述、版本等）
- 配置构建选项（文档生成、警告处理）
- 设置发布配置（单文件发布、自包含部署）
- 启用DPI感知和Windows主题支持

### 数据模型层开发 (已完成)

#### 1. 核心实体模型
创建了5个核心实体模型，完整实现了许可证管理系统的数据结构：

**License（许可证实体）**
- 包含许可证的基本信息：应用ID、许可证密钥、有效期、客户信息等
- 支持硬件绑定、功能特性配置、状态管理
- 提供计算属性：过期状态、剩余天数、状态描述
- 配置了完整的数据验证和约束

**AuthorizationType（授权类型实体）**
- 定义不同的许可证类型：试用版、标准版、专业版、企业版
- 支持功能特性列表、用户数量限制、有效期配置
- 包含价格信息、优先级、试用期设置
- 提供显示名称、价格显示等计算属性

**AppTemplate（应用程序模板实体）**
- 存储应用程序的权限配置模板和相关信息
- 支持加密密钥管理、版本控制、标签分类
- 包含开发商信息、系统要求、联系方式
- 提供模板状态检查、标签解析等功能

**LicenseInfo（许可证信息实体）**
- 存储许可证的扩展属性和自定义信息
- 支持多种属性类型：字符串、整数、布尔值、日期时间等
- 包含属性验证、敏感信息保护、分组显示
- 提供类型转换、值验证等工具方法

**HardwareFingerprint（硬件指纹实体）**
- 存储设备硬件信息用于硬件绑定
- 支持多级指纹强度、部分匹配、虚拟机检测
- 包含处理器、主板、硬盘、MAC地址等硬件信息
- 提供指纹生成、匹配度计算等算法

#### 2. Entity Framework Core配置
**LicenseDbContext数据库上下文**
- 配置了所有实体的映射关系和约束
- 设置了合适的索引以优化查询性能
- 实现了自动时间戳更新机制
- 添加了种子数据用于系统初始化

**数据库配置特点**
- 使用SQLite作为数据存储
- 配置了外键关系和级联删除策略
- 设置了唯一约束和复合索引
- 支持软删除和审计跟踪

#### 3. 仓储模式实现
**通用仓储接口（IRepository<T>）**
- 定义了完整的CRUD操作接口
- 支持分页查询、条件筛选、排序
- 提供事务管理和批量操作
- 包含异步操作和性能优化

**通用仓储实现（Repository<T>）**
- 基于Entity Framework Core的通用实现
- 支持表达式树查询和动态排序
- 实现了事务管理和错误处理
- 提供了扩展点供特定仓储继承

**许可证专用仓储（ILicenseRepository）**
- 扩展了许可证相关的专门查询方法
- 支持许可证验证、统计分析、搜索功能
- 提供了业务逻辑相关的操作方法
- 定义了验证结果和统计信息的数据结构

#### 4. 数据模型特性
- **完整的数据验证**：使用Data Annotations进行字段验证
- **计算属性**：提供业务逻辑相关的计算属性
- **类型安全**：启用可空引用类型，提高代码安全性
- **性能优化**：合理的索引设计和查询优化
- **扩展性**：支持自定义属性和模板化配置

#### 5. 构建验证
数据模型层构建成功，无编译错误：
- 构建时间：4.0秒
- 状态：成功
- 包含：5个实体模型 + 1个数据库上下文 + 完整的仓储模式

### 服务层基础开发 (已完成)

#### 1. 核心服务接口设计
创建了5个核心服务接口，完整定义了许可证管理系统的业务逻辑层：

**ILicenseService（许可证服务接口）**
- 提供许可证生成、验证、激活、管理等核心功能
- 支持单个和批量许可证操作
- 包含许可证搜索、统计、导入导出功能
- 定义了完整的请求/响应数据模型

**IEncryptionService（加密服务接口）**
- 提供RSA、AES等多种加密算法支持
- 支持密钥生成、数字签名、完整性验证
- 包含许可证专用的加密/解密方法
- 提供多种编码格式转换功能

**IValidationService（验证服务接口）**
- 提供全面的许可证验证功能
- 支持硬件指纹、功能特性、用户限制等验证
- 包含批量验证和自定义验证规则
- 提供多级验证结果和详细错误信息

**IHardwareFingerprintService（硬件指纹服务接口）**
- 提供硬件信息收集和指纹生成功能
- 支持多级指纹强度和虚拟机检测
- 包含指纹匹配、优化、导入导出功能
- 提供详细的硬件组件信息获取

**IDataService（数据服务接口）**
- 提供统一的数据访问和管理功能
- 支持数据库备份、恢复、优化操作
- 包含数据导入导出和迁移功能
- 提供数据完整性验证和统计分析

#### 2. 数据传输对象（DTO）设计
为每个服务接口设计了完整的数据传输对象：

**许可证相关DTO**
- LicenseGenerationRequest/Result：许可证生成
- LicenseActivationResult：许可证激活
- LicenseSearchRequest：许可证搜索
- LicenseDetailInfo：许可证详细信息

**加密相关DTO**
- RsaKeyPair：RSA密钥对
- AesKey：AES密钥
- EncryptionResult/DecryptionResult：加密解密结果
- LicenseData：许可证数据结构

**验证相关DTO**
- ValidationResult：验证结果
- BatchValidationResult：批量验证结果
- ValidationOptions：验证选项
- ValidationRule：验证规则

**硬件指纹相关DTO**
- FingerprintMatchResult：指纹匹配结果
- ProcessorInfo/MotherboardInfo等：硬件组件信息
- HardwareFingerprintReport：指纹报告

**数据服务相关DTO**
- DatabaseConnectionStatus：数据库连接状态
- DatabaseBackupResult/RestoreResult：备份恢复结果
- DataExportOptions/ImportOptions：导入导出选项

#### 3. 枚举类型定义
定义了完整的枚举类型体系：
- LicenseConflictType：许可证冲突类型
- LicenseFileFormat：许可证文件格式
- ValidationLevel：验证级别
- ValidationRuleType：验证规则类型
- FingerprintExportFormat：指纹导出格式
- DataExportFormat：数据导出格式

#### 4. 接口设计特点
- **异步优先**：所有I/O操作都使用异步方法
- **类型安全**：启用可空引用类型，明确参数要求
- **扩展性强**：支持选项模式和策略模式
- **错误处理**：完整的错误信息和状态码
- **性能考虑**：支持批量操作和分页查询

#### 5. 业务逻辑覆盖
服务接口覆盖了许可证管理系统的所有核心业务场景：
- 许可证生命周期管理
- 多种加密算法支持
- 全面的验证机制
- 硬件绑定和指纹识别
- 数据管理和维护

#### 6. 构建验证
服务层接口构建成功，无编译错误：
- 构建时间：2.3秒
- 状态：成功
- 包含：5个服务接口 + 50+个DTO类 + 完整的枚举定义

### 工具类库开发 (已完成)

#### 1. 核心工具类实现
创建了5个核心工具类，提供完整的基础功能支持：

**CryptoHelper（加密工具类）**
- 提供RSA、AES等多种加密算法的完整实现
- 支持密钥生成、数字签名、完整性验证
- 包含Base64、十六进制编码转换功能
- 提供密码哈希（PBKDF2）和HMAC签名功能
- 专门的许可证加密/解密方法

**FileHelper（文件操作工具类）**
- 提供安全的文件读写操作（原子性写入）
- 支持文件压缩、解压缩、备份功能
- 包含JSON对象序列化/反序列化
- 提供文件监控、路径验证、目录管理
- 支持临时文件处理和批量文件操作

**ValidationHelper（验证工具类）**
- 提供全面的数据验证功能
- 支持邮箱、URL、IP地址、MAC地址验证
- 包含许可证密钥、硬件指纹格式验证
- 提供对象属性验证和批量验证
- 支持自定义验证规则和正则表达式验证

**SystemHelper（系统信息工具类）**
- 提供完整的硬件信息收集功能
- 支持处理器、主板、硬盘、内存、显卡信息获取
- 包含网络适配器、BIOS、操作系统信息
- 提供虚拟机检测和系统UUID获取
- 支持系统性能监控和硬件变更检测

**DateTimeHelper（日期时间工具类）**
- 提供丰富的日期时间处理功能
- 支持多种格式化和解析方法
- 包含工作日计算、时间范围验证
- 提供相对时间描述和Unix时间戳转换
- 专门的许可证时间信息计算功能

#### 2. 技术实现特点
- **类型安全**：全面启用可空引用类型，提高代码安全性
- **异步优先**：所有I/O操作都采用异步模式
- **错误处理**：完善的异常处理和错误返回机制
- **性能优化**：使用高效的算法和数据结构
- **跨平台兼容**：基于.NET 9的跨平台API设计

#### 3. 全局配置优化
- **GlobalUsings.cs**：统一管理全局using声明
- **隐式全局using**：启用.NET 6+的隐式全局using功能
- **编译器配置**：优化编译器警告和错误处理
- **文档生成**：启用XML文档生成，提供完整的API文档

#### 4. 功能覆盖范围
**安全加密**
- RSA 2048位密钥生成和加密解密
- AES 256位对称加密
- SHA256、MD5哈希计算
- PBKDF2密码哈希和HMAC签名
- 安全的随机数生成

**文件操作**
- 原子性文件写入和安全读取
- GZip压缩和解压缩
- 文件备份和版本管理
- JSON序列化和反序列化
- 文件监控和变更检测

**数据验证**
- 常见格式验证（邮箱、URL、IP等）
- 业务数据验证（许可证、硬件指纹）
- 对象属性验证和批量验证
- 自定义验证规则支持

**系统信息**
- 完整的硬件信息收集
- 虚拟机环境检测
- 系统性能监控
- 网络适配器信息获取

**日期时间**
- 多种格式化和解析方法
- 工作日和时间范围计算
- 许可证时间管理
- 相对时间和持续时间描述

#### 5. 构建验证
工具类库构建成功，无编译错误：
- 构建时间：2.3秒
- 状态：成功
- 包含：5个工具类 + 20+个辅助类型 + 全局配置优化

#### 6. Visual Studio解决方案创建
- 创建解决方案文件：`LicenseManager.sln`
- 添加主项目：`LicenseManager/LicenseManager.csproj`
- 创建测试项目：`LicenseManager.Tests/LicenseManager.Tests.csproj`
- 配置项目引用：测试项目引用主项目
- 支持多平台构建配置：Debug/Release × Any CPU/x64/x86

#### 7. 构建验证
解决方案构建成功，包含两个项目：
- **主项目**: `LicenseManager\bin\Debug\net9.0-windows\win-x64\LicenseManager.dll`
- **测试项目**: `LicenseManager.Tests\bin\Debug\net9.0-windows\LicenseManager.Tests.dll`
- 构建时间：4.1秒
- 状态：成功（4个警告，主要是AutoMapper版本兼容性警告）

#### 8. 解决方案结构
```
LicenseManager.sln                 # Visual Studio解决方案文件
├── LicenseManager/                 # 主WPF应用程序项目
│   ├── Views/                      # XAML视图文件
│   ├── ViewModels/                 # 视图模型类
│   ├── Models/                     # 数据模型类
│   ├── Services/                   # 业务服务层
│   ├── Data/                       # 数据访问层
│   ├── Utils/                      # 工具类
│   ├── Resources/                  # 资源文件
│   ├── appsettings.json           # 应用配置
│   └── app.manifest               # 应用清单
├── LicenseManager.Tests/           # NUnit测试项目
│   └── UnitTest1.cs               # 示例测试文件
└── README.md                       # 项目文档
```

## 项目开发阶段规划

### 第一阶段：项目创建与初始化
1. **项目创建**：使用 .NET CLI 创建 WPF 项目，配置 .NET 9 目标框架
2. **项目结构搭建**：按照规划创建完整的目录结构（Views、ViewModels、Models、Services等）
3. **NuGet包管理**：安装所有必需的依赖包和工具库
4. **基础配置**：创建数据库连接、日志记录、依赖注入等配置文件
5. **项目文件配置**：优化 .csproj 文件设置和构建选项

### 第二阶段：核心基础设施开发
1. **数据模型层**：创建所有实体模型和数据库上下文
2. **服务层基础**：实现核心服务接口和基础服务类
3. **工具类库**：开发加密、验证、文件操作等工具类
4. **MVVM基础设施**：创建基础ViewModel和命令实现

### 第三阶段：功能模块开发
按照业务优先级逐步开发各个功能模块的View和ViewModel

## 开发状态跟踪
| 开发阶段/功能模块      | 项目状态 | View状态 | ViewModel状态 | 负责人 | 计划完成日期 | 实际完成日期 | 备注与链接 |
|----------------------|----------|----------|---------------|--------|--------------|--------------|-----------|
| **第一阶段：项目初始化** |          |          |               |        |              |              |           |
| 项目创建              | 已完成   | -        | -             | AI     | 2024-12-10   | 2024-12-10   | .NET 9 WPF项目创建成功 |
| 目录结构搭建          | 已完成   | -        | -             | AI     | 2024-12-10   | 2024-12-10   | MVVM目录结构创建完成 |
| NuGet包安装          | 已完成   | -        | -             | AI     | 2024-12-10   | 2024-12-10   | 所有依赖包安装成功 |
| 基础配置文件          | 已完成   | -        | -             | AI     | 2024-12-10   | 2024-12-10   | appsettings.json等配置文件创建 |
| 项目文件配置          | 已完成   | -        | -             | AI     | 2024-12-10   | 2024-12-10   | .csproj优化配置完成 |
| **第二阶段：基础设施** |          |          |               |        |              |              |           |
| 数据模型层            | 已完成   | -        | -             | AI     | 2024-12-11   | 2024-12-10   | 完整的EF Core数据模型 |
| 服务层基础            | 已完成   | -        | -             | AI     | 2024-12-12   | 2024-12-10   | 完整的服务接口定义 |
| 工具类库              | 已完成   | -        | -             | AI     | 2024-12-12   | 2024-12-10   | 完整的工具类实现 |
| MVVM基础设施         | 未开始   | -        | -             | AI     | 2024-12-13   |              |           |
| **第三阶段：功能模块** |          |          |               |        |              |              |           |
| 主窗口和导航          | 未开始   | 未开始   | 未开始        | AI     | 2024-12-14   |              |           |
| License生成模块       | 未开始   | 未开始   | 未开始        | AI     | 2024-12-15   |              |           |
| License验证模块       | 未开始   | 未开始   | 未开始        | AI     | 2024-12-16   |              |           |
| 授权类型管理模块       | 未开始   | 未开始   | 未开始        | AI     | 2024-12-17   |              |           |
| 应用权限模板管理模块   | 未开始   | 未开始   | 未开始        | AI     | 2024-12-18   |              |           |
| 批量License生成模块   | 未开始   | 未开始   | 未开始        | AI     | 2024-12-19   |              |           |
| License信息管理模块   | 未开始   | 未开始   | 未开始        | AI     | 2024-12-20   |              |           |
| 系统设置模块          | 未开始   | 未开始   | 未开始        | AI     | 2024-12-21   |              |           |

## 代码检查与问题记录
[本部分用于记录WPF代码检查结果和开发过程中遇到的问题及其解决方案，包括XAML验证、数据绑定问题、性能优化等。]

## 环境设置与运行指南

### 开发环境要求
- **操作系统**: Windows 10/11 (x64)
- **.NET SDK**: .NET 9.0 或更高版本
- **IDE**: Visual Studio 2022 17.8+ 或 Visual Studio Code + C# 扩展
- **数据库**: SQLite (无需额外安装)

### 项目初始化步骤

#### 1. 项目创建
```powershell
# 创建WPF项目
dotnet new wpf -n LicenseManager -f net9.0

# 进入项目目录
cd LicenseManager
```

#### 2. 项目结构搭建
```powershell
# 创建主要目录结构
New-Item -ItemType Directory -Path "Views"
New-Item -ItemType Directory -Path "ViewModels"
New-Item -ItemType Directory -Path "Models"
New-Item -ItemType Directory -Path "Services"
New-Item -ItemType Directory -Path "Converters"
New-Item -ItemType Directory -Path "Controls"
New-Item -ItemType Directory -Path "Resources"
New-Item -ItemType Directory -Path "Resources/Styles"
New-Item -ItemType Directory -Path "Resources/Templates"
New-Item -ItemType Directory -Path "Resources/Images"
New-Item -ItemType Directory -Path "Data"
New-Item -ItemType Directory -Path "Data/Repositories"
New-Item -ItemType Directory -Path "Utils"
```

#### 3. NuGet包安装
```powershell
# Entity Framework Core (SQLite)
dotnet add package Microsoft.EntityFrameworkCore.Sqlite
dotnet add package Microsoft.EntityFrameworkCore.Tools
dotnet add package Microsoft.EntityFrameworkCore.Design

# MVVM工具包
dotnet add package CommunityToolkit.Mvvm

# 依赖注入
dotnet add package Microsoft.Extensions.DependencyInjection
dotnet add package Microsoft.Extensions.Hosting

# 日志记录
dotnet add package Serilog
dotnet add package Serilog.Sinks.File
dotnet add package Serilog.Extensions.Hosting

# JSON处理
dotnet add package Newtonsoft.Json

# 对象映射
dotnet add package AutoMapper
dotnet add package AutoMapper.Extensions.Microsoft.DependencyInjection

# 测试框架
dotnet add package NUnit
dotnet add package NUnit3TestAdapter
dotnet add package Microsoft.NET.Test.Sdk

# Excel处理（用于批量导入）
dotnet add package EPPlus

# 加密库
dotnet add package System.Security.Cryptography.Algorithms
```

#### 4. 项目文件配置 (.csproj)
项目文件将配置以下属性：
- 目标框架：net9.0-windows
- 输出类型：WinExe
- 使用WPF：true
- 可空引用类型：enable
- 隐式全局using：enable

#### 5. 基础配置文件
将创建以下配置文件：
- `appsettings.json`: 应用程序配置
- `serilog.json`: 日志配置
- `database.json`: 数据库连接配置

### 运行和调试

#### 使用.NET CLI
```powershell
# 构建整个解决方案
dotnet build LicenseManager.sln

# 构建特定项目
dotnet build LicenseManager/LicenseManager.csproj

# 运行主项目
dotnet run --project LicenseManager

# 运行测试
dotnet test LicenseManager.Tests

# 发布项目
dotnet publish LicenseManager -c Release -r win-x64 --self-contained
```

#### 使用Visual Studio
1. 打开 `LicenseManager.sln` 解决方案文件
2. 设置 `LicenseManager` 为启动项目
3. 按 F5 运行或 Ctrl+F5 调试运行
4. 使用测试资源管理器运行单元测试

#### 项目输出
- **调试版本**: `LicenseManager\bin\Debug\net9.0-windows\win-x64\`
- **发布版本**: `LicenseManager\bin\Release\net9.0-windows\win-x64\`
- **测试结果**: `LicenseManager.Tests\bin\Debug\net9.0-windows\`

## WPF性能优化
[记录WPF应用程序的性能优化策略和实施情况。]

## 部署指南
[包含WPF应用程序的打包、发布和部署相关信息。]