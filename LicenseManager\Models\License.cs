using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace LicenseManager.Models;

/// <summary>
/// 许可证实体模型
/// 表示系统中的一个软件许可证记录
/// </summary>
[Table("Licenses")]
public class License
{
    /// <summary>
    /// 许可证唯一标识符
    /// </summary>
    [Key]
    public int Id { get; set; }

    /// <summary>
    /// 应用程序标识符
    /// </summary>
    [Required]
    [StringLength(100)]
    public string AppId { get; set; } = string.Empty;

    /// <summary>
    /// 应用程序名称
    /// </summary>
    [Required]
    [StringLength(200)]
    public string AppName { get; set; } = string.Empty;

    /// <summary>
    /// 授权类型外键
    /// </summary>
    [Required]
    public int AuthorizationTypeId { get; set; }

    /// <summary>
    /// 许可证密钥（加密后的许可证内容）
    /// </summary>
    [Required]
    [StringLength(4000)]
    public string LicenseKey { get; set; } = string.Empty;

    /// <summary>
    /// 硬件指纹（用于硬件绑定）
    /// </summary>
    [StringLength(500)]
    public string? HardwareFingerprint { get; set; }

    /// <summary>
    /// 许可证颁发日期
    /// </summary>
    [Required]
    public DateTime IssueDate { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 许可证过期日期
    /// </summary>
    [Required]
    public DateTime ExpiryDate { get; set; }

    /// <summary>
    /// 许可证是否激活
    /// </summary>
    [Required]
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// 客户名称
    /// </summary>
    [StringLength(200)]
    public string? CustomerName { get; set; }

    /// <summary>
    /// 客户邮箱
    /// </summary>
    [StringLength(200)]
    [EmailAddress]
    public string? CustomerEmail { get; set; }

    /// <summary>
    /// 最大用户数量（-1表示无限制）
    /// </summary>
    public int MaxUsers { get; set; } = 1;

    /// <summary>
    /// 许可证功能特性（JSON格式存储）
    /// </summary>
    [StringLength(2000)]
    public string? Features { get; set; }

    /// <summary>
    /// 备注信息
    /// </summary>
    [StringLength(1000)]
    public string? Notes { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    [Required]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 最后更新时间
    /// </summary>
    [Required]
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    // 导航属性
    /// <summary>
    /// 关联的授权类型
    /// </summary>
    [ForeignKey(nameof(AuthorizationTypeId))]
    public virtual AuthorizationType? AuthorizationType { get; set; }

    /// <summary>
    /// 关联的许可证详细信息
    /// </summary>
    public virtual ICollection<LicenseInfo> LicenseInfos { get; set; } = new List<LicenseInfo>();

    /// <summary>
    /// 计算属性：许可证是否已过期
    /// </summary>
    [NotMapped]
    public bool IsExpired => DateTime.UtcNow > ExpiryDate;

    /// <summary>
    /// 计算属性：许可证剩余天数
    /// </summary>
    [NotMapped]
    public int DaysRemaining => Math.Max(0, (ExpiryDate - DateTime.UtcNow).Days);

    /// <summary>
    /// 计算属性：许可证状态描述
    /// </summary>
    [NotMapped]
    public string StatusDescription
    {
        get
        {
            if (!IsActive) return "已禁用";
            if (IsExpired) return "已过期";
            if (DaysRemaining <= 7) return "即将过期";
            return "正常";
        }
    }
}
