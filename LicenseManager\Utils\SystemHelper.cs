using System.Diagnostics;
using System.Management;
using System.Net.NetworkInformation;
using System.Runtime.InteropServices;
using Microsoft.Win32;

namespace LicenseManager.Utils;

/// <summary>
/// 系统信息工具类
/// 提供获取系统硬件和软件信息的静态方法
/// </summary>
public static class SystemHelper
{
    /// <summary>
    /// 获取处理器信息
    /// </summary>
    /// <returns>处理器信息</returns>
    public static async Task<ProcessorInfo> GetProcessorInfoAsync()
    {
        var info = new ProcessorInfo();

        try
        {
            await Task.Run(() =>
            {
                using var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_Processor");
                foreach (ManagementObject obj in searcher.Get())
                {
                    info.ProcessorId = obj["ProcessorId"]?.ToString() ?? "";
                    info.Name = obj["Name"]?.ToString() ?? "";
                    info.Manufacturer = obj["Manufacturer"]?.ToString() ?? "";
                    info.Architecture = obj["Architecture"]?.ToString() ?? "";
                    info.CoreCount = Convert.ToInt32(obj["NumberOfCores"] ?? 0);
                    info.ThreadCount = Convert.ToInt32(obj["NumberOfLogicalProcessors"] ?? 0);
                    info.BaseFrequency = Convert.ToInt32(obj["CurrentClockSpeed"] ?? 0);
                    info.MaxFrequency = Convert.ToInt32(obj["MaxClockSpeed"] ?? 0);
                    break; // 只取第一个处理器
                }
            });
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"获取处理器信息失败: {ex.Message}");
        }

        return info;
    }

    /// <summary>
    /// 获取主板信息
    /// </summary>
    /// <returns>主板信息</returns>
    public static async Task<MotherboardInfo> GetMotherboardInfoAsync()
    {
        var info = new MotherboardInfo();

        try
        {
            await Task.Run(() =>
            {
                using var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_BaseBoard");
                foreach (ManagementObject obj in searcher.Get())
                {
                    info.SerialNumber = obj["SerialNumber"]?.ToString() ?? "";
                    info.Manufacturer = obj["Manufacturer"]?.ToString() ?? "";
                    info.Product = obj["Product"]?.ToString() ?? "";
                    info.Version = obj["Version"]?.ToString() ?? "";
                    break;
                }
            });
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"获取主板信息失败: {ex.Message}");
        }

        return info;
    }

    /// <summary>
    /// 获取硬盘信息
    /// </summary>
    /// <returns>硬盘信息列表</returns>
    public static async Task<List<DiskInfo>> GetDiskInfoAsync()
    {
        var disks = new List<DiskInfo>();

        try
        {
            await Task.Run(() =>
            {
                using var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_DiskDrive");
                foreach (ManagementObject obj in searcher.Get())
                {
                    var disk = new DiskInfo
                    {
                        SerialNumber = obj["SerialNumber"]?.ToString()?.Trim() ?? "",
                        Model = obj["Model"]?.ToString() ?? "",
                        Manufacturer = obj["Manufacturer"]?.ToString() ?? "",
                        Capacity = Convert.ToInt64(obj["Size"] ?? 0),
                        InterfaceType = obj["InterfaceType"]?.ToString() ?? ""
                    };

                    // 检查是否为系统盘
                    var deviceId = obj["DeviceID"]?.ToString();
                    if (!string.IsNullOrEmpty(deviceId))
                    {
                        disk.IsSystemDisk = IsSystemDisk(deviceId);
                    }

                    disks.Add(disk);
                }
            });
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"获取硬盘信息失败: {ex.Message}");
        }

        return disks;
    }

    /// <summary>
    /// 获取网络适配器信息
    /// </summary>
    /// <returns>网络适配器信息列表</returns>
    public static async Task<List<NetworkAdapterInfo>> GetNetworkAdapterInfoAsync()
    {
        var adapters = new List<NetworkAdapterInfo>();

        try
        {
            await Task.Run(() =>
            {
                var networkInterfaces = NetworkInterface.GetAllNetworkInterfaces();
                foreach (var ni in networkInterfaces)
                {
                    if (ni.NetworkInterfaceType == NetworkInterfaceType.Loopback)
                        continue;

                    var adapter = new NetworkAdapterInfo
                    {
                        MacAddress = ni.GetPhysicalAddress().ToString(),
                        Name = ni.Name,
                        Description = ni.Description,
                        IsEnabled = ni.OperationalStatus == OperationalStatus.Up,
                        NetworkType = ni.NetworkInterfaceType.ToString()
                    };

                    adapters.Add(adapter);
                }
            });
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"获取网络适配器信息失败: {ex.Message}");
        }

        return adapters;
    }

    /// <summary>
    /// 获取内存信息
    /// </summary>
    /// <returns>内存信息</returns>
    public static async Task<MemoryInfo> GetMemoryInfoAsync()
    {
        var info = new MemoryInfo();
        var modules = new List<MemoryModuleInfo>();

        try
        {
            await Task.Run(() =>
            {
                // 获取总内存
                using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_ComputerSystem"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        info.TotalMemory = Convert.ToInt64(obj["TotalPhysicalMemory"] ?? 0);
                        break;
                    }
                }

                // 获取可用内存
                using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_OperatingSystem"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        info.AvailableMemory = Convert.ToInt64(obj["FreePhysicalMemory"] ?? 0) * 1024;
                        break;
                    }
                }

                // 获取内存条信息
                using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_PhysicalMemory"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        var module = new MemoryModuleInfo
                        {
                            SerialNumber = obj["SerialNumber"]?.ToString() ?? "",
                            Capacity = Convert.ToInt64(obj["Capacity"] ?? 0),
                            Manufacturer = obj["Manufacturer"]?.ToString() ?? "",
                            Speed = Convert.ToInt32(obj["Speed"] ?? 0)
                        };
                        modules.Add(module);
                    }
                }
            });
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"获取内存信息失败: {ex.Message}");
        }

        info.Modules = modules;
        return info;
    }

    /// <summary>
    /// 获取显卡信息
    /// </summary>
    /// <returns>显卡信息列表</returns>
    public static async Task<List<GraphicsInfo>> GetGraphicsInfoAsync()
    {
        var graphics = new List<GraphicsInfo>();

        try
        {
            await Task.Run(() =>
            {
                using var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_VideoController");
                foreach (ManagementObject obj in searcher.Get())
                {
                    var gpu = new GraphicsInfo
                    {
                        Name = obj["Name"]?.ToString() ?? "",
                        Manufacturer = obj["AdapterCompatibility"]?.ToString() ?? "",
                        VideoMemory = Convert.ToInt64(obj["AdapterRAM"] ?? 0),
                        DriverVersion = obj["DriverVersion"]?.ToString() ?? ""
                    };
                    graphics.Add(gpu);
                }
            });
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"获取显卡信息失败: {ex.Message}");
        }

        return graphics;
    }

    /// <summary>
    /// 获取BIOS信息
    /// </summary>
    /// <returns>BIOS信息</returns>
    public static async Task<BiosInfo> GetBiosInfoAsync()
    {
        var info = new BiosInfo();

        try
        {
            await Task.Run(() =>
            {
                using var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_BIOS");
                foreach (ManagementObject obj in searcher.Get())
                {
                    info.SerialNumber = obj["SerialNumber"]?.ToString() ?? "";
                    info.Manufacturer = obj["Manufacturer"]?.ToString() ?? "";
                    info.Version = obj["Version"]?.ToString() ?? "";
                    
                    var releaseDateStr = obj["ReleaseDate"]?.ToString();
                    if (!string.IsNullOrEmpty(releaseDateStr) && releaseDateStr.Length >= 8)
                    {
                        if (DateTime.TryParseExact(releaseDateStr.Substring(0, 8), "yyyyMMdd", null, System.Globalization.DateTimeStyles.None, out var releaseDate))
                        {
                            info.ReleaseDate = releaseDate;
                        }
                    }
                    break;
                }
            });
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"获取BIOS信息失败: {ex.Message}");
        }

        return info;
    }

    /// <summary>
    /// 获取操作系统信息
    /// </summary>
    /// <returns>操作系统信息</returns>
    public static async Task<OperatingSystemInfo> GetOperatingSystemInfoAsync()
    {
        var info = new OperatingSystemInfo();

        try
        {
            await Task.Run(() =>
            {
                info.Name = Environment.OSVersion.VersionString;
                info.Version = Environment.OSVersion.Version.ToString();
                info.Architecture = RuntimeInformation.OSArchitecture.ToString();

                using var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_OperatingSystem");
                foreach (ManagementObject obj in searcher.Get())
                {
                    info.SerialNumber = obj["SerialNumber"]?.ToString() ?? "";
                    
                    var installDateStr = obj["InstallDate"]?.ToString();
                    if (!string.IsNullOrEmpty(installDateStr))
                    {
                        if (ManagementDateTimeConverter.ToDateTime(installDateStr) is DateTime installDate)
                        {
                            info.InstallDate = installDate;
                        }
                    }
                    break;
                }
            });
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"获取操作系统信息失败: {ex.Message}");
        }

        return info;
    }

    /// <summary>
    /// 检测虚拟机
    /// </summary>
    /// <returns>虚拟机检测结果</returns>
    public static async Task<VirtualMachineDetectionResult> DetectVirtualMachineAsync()
    {
        var result = new VirtualMachineDetectionResult();
        var detectionMethods = new List<string>();
        var vmIndicators = 0;

        try
        {
            await Task.Run(() =>
            {
                // 检查制造商信息
                var manufacturers = new[] { "VMware", "VirtualBox", "Microsoft Corporation", "Xen", "QEMU" };
                using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_ComputerSystem"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        var manufacturer = obj["Manufacturer"]?.ToString() ?? "";
                        var model = obj["Model"]?.ToString() ?? "";
                        
                        if (manufacturers.Any(m => manufacturer.Contains(m, StringComparison.OrdinalIgnoreCase)) ||
                            model.Contains("Virtual", StringComparison.OrdinalIgnoreCase))
                        {
                            vmIndicators++;
                            detectionMethods.Add("Manufacturer/Model");
                            
                            if (manufacturer.Contains("VMware", StringComparison.OrdinalIgnoreCase))
                                result.VirtualMachineType = "VMware";
                            else if (manufacturer.Contains("VirtualBox", StringComparison.OrdinalIgnoreCase))
                                result.VirtualMachineType = "VirtualBox";
                            else if (manufacturer.Contains("Microsoft", StringComparison.OrdinalIgnoreCase))
                                result.VirtualMachineType = "Hyper-V";
                        }
                        break;
                    }
                }

                // 检查BIOS信息
                using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_BIOS"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        var version = obj["Version"]?.ToString() ?? "";
                        if (version.Contains("VBOX", StringComparison.OrdinalIgnoreCase) ||
                            version.Contains("VMware", StringComparison.OrdinalIgnoreCase))
                        {
                            vmIndicators++;
                            detectionMethods.Add("BIOS");
                        }
                        break;
                    }
                }

                // 检查硬盘型号
                using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_DiskDrive"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        var model = obj["Model"]?.ToString() ?? "";
                        if (model.Contains("VBOX", StringComparison.OrdinalIgnoreCase) ||
                            model.Contains("VMware", StringComparison.OrdinalIgnoreCase) ||
                            model.Contains("Virtual", StringComparison.OrdinalIgnoreCase))
                        {
                            vmIndicators++;
                            detectionMethods.Add("Disk Model");
                            break;
                        }
                    }
                }
            });
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"虚拟机检测失败: {ex.Message}");
        }

        result.IsVirtualMachine = vmIndicators > 0;
        result.DetectionMethods = detectionMethods.ToArray();
        result.Confidence = Math.Min(1.0m, vmIndicators * 0.4m);

        return result;
    }

    /// <summary>
    /// 获取系统UUID
    /// </summary>
    /// <returns>系统UUID</returns>
    public static async Task<string> GetSystemUuidAsync()
    {
        try
        {
            return await Task.Run(() =>
            {
                using var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_ComputerSystemProduct");
                foreach (ManagementObject obj in searcher.Get())
                {
                    return obj["UUID"]?.ToString() ?? "";
                }
                return "";
            });
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"获取系统UUID失败: {ex.Message}");
            return "";
        }
    }

    /// <summary>
    /// 获取计算机名称
    /// </summary>
    /// <returns>计算机名称</returns>
    public static string GetComputerName()
    {
        return Environment.MachineName;
    }

    /// <summary>
    /// 获取用户名
    /// </summary>
    /// <returns>用户名</returns>
    public static string GetUserName()
    {
        return Environment.UserName;
    }

    /// <summary>
    /// 获取时区信息
    /// </summary>
    /// <returns>时区信息</returns>
    public static string GetTimeZone()
    {
        return TimeZoneInfo.Local.Id;
    }

    /// <summary>
    /// 检查是否为系统盘
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <returns>是否为系统盘</returns>
    private static bool IsSystemDisk(string deviceId)
    {
        try
        {
            var systemDrive = Environment.GetEnvironmentVariable("SystemDrive") ?? "C:";
            using var searcher = new ManagementObjectSearcher($"ASSOCIATORS OF {{Win32_DiskDrive.DeviceID='{deviceId}'}} WHERE AssocClass = Win32_DiskDriveToDiskPartition");
            
            foreach (ManagementObject partition in searcher.Get())
            {
                using var logicalSearcher = new ManagementObjectSearcher($"ASSOCIATORS OF {{Win32_DiskPartition.DeviceID='{partition["DeviceID"]}'}} WHERE AssocClass = Win32_LogicalDiskToPartition");
                
                foreach (ManagementObject logical in logicalSearcher.Get())
                {
                    var driveLetter = logical["DeviceID"]?.ToString();
                    if (driveLetter == systemDrive)
                    {
                        return true;
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"检查系统盘失败: {ex.Message}");
        }

        return false;
    }

    /// <summary>
    /// 获取系统性能信息
    /// </summary>
    /// <returns>性能信息</returns>
    public static SystemPerformanceInfo GetSystemPerformance()
    {
        var info = new SystemPerformanceInfo();

        try
        {
            // CPU使用率
            using var cpuCounter = new PerformanceCounter("Processor", "% Processor Time", "_Total");
            cpuCounter.NextValue(); // 第一次调用返回0
            Thread.Sleep(100);
            info.CpuUsage = cpuCounter.NextValue();

            // 内存使用率
            var totalMemory = GC.GetTotalMemory(false);
            var availableMemory = GC.GetTotalMemory(true);
            info.MemoryUsage = totalMemory;
            info.AvailableMemory = availableMemory;
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"获取性能信息失败: {ex.Message}");
        }

        return info;
    }
}

/// <summary>
/// 系统性能信息
/// </summary>
public class SystemPerformanceInfo
{
    /// <summary>
    /// CPU使用率（百分比）
    /// </summary>
    public float CpuUsage { get; set; }

    /// <summary>
    /// 内存使用量（字节）
    /// </summary>
    public long MemoryUsage { get; set; }

    /// <summary>
    /// 可用内存（字节）
    /// </summary>
    public long AvailableMemory { get; set; }
}

/// <summary>
/// 处理器信息
/// </summary>
public class ProcessorInfo
{
    /// <summary>
    /// 处理器ID
    /// </summary>
    public string ProcessorId { get; set; } = string.Empty;

    /// <summary>
    /// 处理器名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 制造商
    /// </summary>
    public string Manufacturer { get; set; } = string.Empty;

    /// <summary>
    /// 架构
    /// </summary>
    public string Architecture { get; set; } = string.Empty;

    /// <summary>
    /// 核心数
    /// </summary>
    public int CoreCount { get; set; }

    /// <summary>
    /// 线程数
    /// </summary>
    public int ThreadCount { get; set; }

    /// <summary>
    /// 基础频率（MHz）
    /// </summary>
    public int BaseFrequency { get; set; }

    /// <summary>
    /// 最大频率（MHz）
    /// </summary>
    public int MaxFrequency { get; set; }
}

/// <summary>
/// 主板信息
/// </summary>
public class MotherboardInfo
{
    /// <summary>
    /// 主板序列号
    /// </summary>
    public string SerialNumber { get; set; } = string.Empty;

    /// <summary>
    /// 制造商
    /// </summary>
    public string Manufacturer { get; set; } = string.Empty;

    /// <summary>
    /// 产品名称
    /// </summary>
    public string Product { get; set; } = string.Empty;

    /// <summary>
    /// 版本
    /// </summary>
    public string Version { get; set; } = string.Empty;
}

/// <summary>
/// 硬盘信息
/// </summary>
public class DiskInfo
{
    /// <summary>
    /// 硬盘序列号
    /// </summary>
    public string SerialNumber { get; set; } = string.Empty;

    /// <summary>
    /// 型号
    /// </summary>
    public string Model { get; set; } = string.Empty;

    /// <summary>
    /// 制造商
    /// </summary>
    public string Manufacturer { get; set; } = string.Empty;

    /// <summary>
    /// 容量（字节）
    /// </summary>
    public long Capacity { get; set; }

    /// <summary>
    /// 接口类型
    /// </summary>
    public string InterfaceType { get; set; } = string.Empty;

    /// <summary>
    /// 是否为系统盘
    /// </summary>
    public bool IsSystemDisk { get; set; }
}

/// <summary>
/// 网络适配器信息
/// </summary>
public class NetworkAdapterInfo
{
    /// <summary>
    /// MAC地址
    /// </summary>
    public string MacAddress { get; set; } = string.Empty;

    /// <summary>
    /// 适配器名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; }

    /// <summary>
    /// 网络类型
    /// </summary>
    public string NetworkType { get; set; } = string.Empty;
}

/// <summary>
/// 内存信息
/// </summary>
public class MemoryInfo
{
    /// <summary>
    /// 总内存（字节）
    /// </summary>
    public long TotalMemory { get; set; }

    /// <summary>
    /// 可用内存（字节）
    /// </summary>
    public long AvailableMemory { get; set; }

    /// <summary>
    /// 内存条信息
    /// </summary>
    public IEnumerable<MemoryModuleInfo> Modules { get; set; } = new List<MemoryModuleInfo>();
}

/// <summary>
/// 内存条信息
/// </summary>
public class MemoryModuleInfo
{
    /// <summary>
    /// 序列号
    /// </summary>
    public string SerialNumber { get; set; } = string.Empty;

    /// <summary>
    /// 容量（字节）
    /// </summary>
    public long Capacity { get; set; }

    /// <summary>
    /// 制造商
    /// </summary>
    public string Manufacturer { get; set; } = string.Empty;

    /// <summary>
    /// 速度（MHz）
    /// </summary>
    public int Speed { get; set; }
}

/// <summary>
/// 显卡信息
/// </summary>
public class GraphicsInfo
{
    /// <summary>
    /// 显卡名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 制造商
    /// </summary>
    public string Manufacturer { get; set; } = string.Empty;

    /// <summary>
    /// 显存大小（字节）
    /// </summary>
    public long VideoMemory { get; set; }

    /// <summary>
    /// 驱动版本
    /// </summary>
    public string DriverVersion { get; set; } = string.Empty;
}

/// <summary>
/// BIOS信息
/// </summary>
public class BiosInfo
{
    /// <summary>
    /// BIOS序列号
    /// </summary>
    public string SerialNumber { get; set; } = string.Empty;

    /// <summary>
    /// 制造商
    /// </summary>
    public string Manufacturer { get; set; } = string.Empty;

    /// <summary>
    /// 版本
    /// </summary>
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// 发布日期
    /// </summary>
    public DateTime? ReleaseDate { get; set; }
}

/// <summary>
/// 操作系统信息
/// </summary>
public class OperatingSystemInfo
{
    /// <summary>
    /// 操作系统名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 版本
    /// </summary>
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// 架构
    /// </summary>
    public string Architecture { get; set; } = string.Empty;

    /// <summary>
    /// 安装日期
    /// </summary>
    public DateTime? InstallDate { get; set; }

    /// <summary>
    /// 序列号
    /// </summary>
    public string SerialNumber { get; set; } = string.Empty;
}

/// <summary>
/// 虚拟机检测结果
/// </summary>
public class VirtualMachineDetectionResult
{
    /// <summary>
    /// 是否为虚拟机
    /// </summary>
    public bool IsVirtualMachine { get; set; }

    /// <summary>
    /// 虚拟机类型
    /// </summary>
    public string? VirtualMachineType { get; set; }

    /// <summary>
    /// 检测方法
    /// </summary>
    public string[] DetectionMethods { get; set; } = Array.Empty<string>();

    /// <summary>
    /// 置信度（0.0-1.0）
    /// </summary>
    public decimal Confidence { get; set; }
}
