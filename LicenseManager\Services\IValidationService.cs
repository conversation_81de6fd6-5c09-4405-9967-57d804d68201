using LicenseManager.Models;
using LicenseManager.Data.Repositories;

namespace LicenseManager.Services;

/// <summary>
/// 验证服务接口
/// 提供各种验证功能，包括许可证验证、数据验证等
/// </summary>
public interface IValidationService
{
    /// <summary>
    /// 验证许可证基本信息
    /// </summary>
    /// <param name="license">许可证对象</param>
    /// <returns>验证结果</returns>
    ValidationResult ValidateLicenseBasicInfo(License license);

    /// <summary>
    /// 验证许可证是否过期
    /// </summary>
    /// <param name="license">许可证对象</param>
    /// <returns>验证结果</returns>
    ValidationResult ValidateLicenseExpiry(License license);

    /// <summary>
    /// 验证硬件指纹匹配
    /// </summary>
    /// <param name="license">许可证对象</param>
    /// <param name="currentFingerprint">当前硬件指纹</param>
    /// <returns>验证结果</returns>
    Task<ValidationResult> ValidateHardwareFingerprintAsync(License license, string currentFingerprint);

    /// <summary>
    /// 验证许可证功能特性
    /// </summary>
    /// <param name="license">许可证对象</param>
    /// <param name="requiredFeatures">需要的功能特性</param>
    /// <returns>验证结果</returns>
    ValidationResult ValidateLicenseFeatures(License license, string[] requiredFeatures);

    /// <summary>
    /// 验证用户数量限制
    /// </summary>
    /// <param name="license">许可证对象</param>
    /// <param name="currentUserCount">当前用户数量</param>
    /// <returns>验证结果</returns>
    ValidationResult ValidateUserLimit(License license, int currentUserCount);

    /// <summary>
    /// 验证许可证完整性
    /// </summary>
    /// <param name="licenseKey">许可证密钥</param>
    /// <param name="publicKey">公钥</param>
    /// <returns>验证结果</returns>
    Task<ValidationResult> ValidateLicenseIntegrityAsync(string licenseKey, string publicKey);

    /// <summary>
    /// 验证应用程序兼容性
    /// </summary>
    /// <param name="license">许可证对象</param>
    /// <param name="appId">应用程序ID</param>
    /// <param name="appVersion">应用程序版本</param>
    /// <returns>验证结果</returns>
    ValidationResult ValidateApplicationCompatibility(License license, string appId, string? appVersion = null);

    /// <summary>
    /// 验证网络许可证
    /// </summary>
    /// <param name="licenseKey">许可证密钥</param>
    /// <param name="serverEndpoint">许可证服务器端点</param>
    /// <returns>验证结果</returns>
    Task<ValidationResult> ValidateNetworkLicenseAsync(string licenseKey, string serverEndpoint);

    /// <summary>
    /// 验证许可证黑名单
    /// </summary>
    /// <param name="licenseKey">许可证密钥</param>
    /// <returns>验证结果</returns>
    Task<ValidationResult> ValidateLicenseBlacklistAsync(string licenseKey);

    /// <summary>
    /// 验证客户信息
    /// </summary>
    /// <param name="customerName">客户名称</param>
    /// <param name="customerEmail">客户邮箱</param>
    /// <returns>验证结果</returns>
    ValidationResult ValidateCustomerInfo(string? customerName, string? customerEmail);

    /// <summary>
    /// 验证授权类型
    /// </summary>
    /// <param name="authorizationType">授权类型</param>
    /// <returns>验证结果</returns>
    ValidationResult ValidateAuthorizationType(AuthorizationType authorizationType);

    /// <summary>
    /// 验证应用程序模板
    /// </summary>
    /// <param name="appTemplate">应用程序模板</param>
    /// <returns>验证结果</returns>
    ValidationResult ValidateAppTemplate(AppTemplate appTemplate);

    /// <summary>
    /// 验证硬件指纹格式
    /// </summary>
    /// <param name="hardwareFingerprint">硬件指纹</param>
    /// <returns>验证结果</returns>
    ValidationResult ValidateHardwareFingerprintFormat(string hardwareFingerprint);

    /// <summary>
    /// 验证许可证生成请求
    /// </summary>
    /// <param name="request">许可证生成请求</param>
    /// <returns>验证结果</returns>
    Task<ValidationResult> ValidateLicenseGenerationRequestAsync(LicenseGenerationRequest request);

    /// <summary>
    /// 批量验证许可证
    /// </summary>
    /// <param name="licenseKeys">许可证密钥列表</param>
    /// <param name="validationOptions">验证选项</param>
    /// <returns>批量验证结果</returns>
    Task<BatchValidationResult> ValidateLicensesBatchAsync(IEnumerable<string> licenseKeys, ValidationOptions? validationOptions = null);

    /// <summary>
    /// 验证许可证文件格式
    /// </summary>
    /// <param name="fileContent">文件内容</param>
    /// <param name="fileName">文件名</param>
    /// <returns>验证结果</returns>
    ValidationResult ValidateLicenseFileFormat(byte[] fileContent, string fileName);

    /// <summary>
    /// 验证系统环境
    /// </summary>
    /// <param name="license">许可证对象</param>
    /// <returns>验证结果</returns>
    ValidationResult ValidateSystemEnvironment(License license);

    /// <summary>
    /// 验证时间同步
    /// </summary>
    /// <returns>验证结果</returns>
    Task<ValidationResult> ValidateTimeSynchronizationAsync();

    /// <summary>
    /// 获取验证规则
    /// </summary>
    /// <param name="ruleType">规则类型</param>
    /// <returns>验证规则列表</returns>
    Task<IEnumerable<ValidationRule>> GetValidationRulesAsync(ValidationRuleType ruleType);

    /// <summary>
    /// 更新验证规则
    /// </summary>
    /// <param name="rule">验证规则</param>
    /// <returns>是否成功</returns>
    Task<bool> UpdateValidationRuleAsync(ValidationRule rule);
}

/// <summary>
/// 验证结果
/// </summary>
public class ValidationResult
{
    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 验证消息
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 错误代码
    /// </summary>
    public string? ErrorCode { get; set; }

    /// <summary>
    /// 验证级别
    /// </summary>
    public ValidationLevel Level { get; set; } = ValidationLevel.Error;

    /// <summary>
    /// 详细信息
    /// </summary>
    public Dictionary<string, object>? Details { get; set; }

    /// <summary>
    /// 验证时间
    /// </summary>
    public DateTime ValidatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 子验证结果
    /// </summary>
    public List<ValidationResult>? SubResults { get; set; }

    /// <summary>
    /// 创建成功的验证结果
    /// </summary>
    /// <param name="message">消息</param>
    /// <returns>验证结果</returns>
    public static ValidationResult Success(string message = "验证成功")
    {
        return new ValidationResult { IsValid = true, Message = message, Level = ValidationLevel.Success };
    }

    /// <summary>
    /// 创建失败的验证结果
    /// </summary>
    /// <param name="message">错误消息</param>
    /// <param name="errorCode">错误代码</param>
    /// <returns>验证结果</returns>
    public static ValidationResult Failure(string message, string? errorCode = null)
    {
        return new ValidationResult { IsValid = false, Message = message, ErrorCode = errorCode, Level = ValidationLevel.Error };
    }

    /// <summary>
    /// 创建警告的验证结果
    /// </summary>
    /// <param name="message">警告消息</param>
    /// <returns>验证结果</returns>
    public static ValidationResult Warning(string message)
    {
        return new ValidationResult { IsValid = true, Message = message, Level = ValidationLevel.Warning };
    }
}

/// <summary>
/// 批量验证结果
/// </summary>
public class BatchValidationResult
{
    /// <summary>
    /// 总数量
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// 成功数量
    /// </summary>
    public int SuccessCount { get; set; }

    /// <summary>
    /// 失败数量
    /// </summary>
    public int FailureCount { get; set; }

    /// <summary>
    /// 详细结果
    /// </summary>
    public Dictionary<string, ValidationResult> Results { get; set; } = new();

    /// <summary>
    /// 验证时间
    /// </summary>
    public DateTime ValidatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 验证耗时（毫秒）
    /// </summary>
    public long ElapsedMilliseconds { get; set; }

    /// <summary>
    /// 成功率
    /// </summary>
    public double SuccessRate => TotalCount > 0 ? (double)SuccessCount / TotalCount : 0;
}

/// <summary>
/// 验证选项
/// </summary>
public class ValidationOptions
{
    /// <summary>
    /// 是否验证过期时间
    /// </summary>
    public bool ValidateExpiry { get; set; } = true;

    /// <summary>
    /// 是否验证硬件指纹
    /// </summary>
    public bool ValidateHardwareFingerprint { get; set; } = true;

    /// <summary>
    /// 是否验证完整性
    /// </summary>
    public bool ValidateIntegrity { get; set; } = true;

    /// <summary>
    /// 是否验证黑名单
    /// </summary>
    public bool ValidateBlacklist { get; set; } = true;

    /// <summary>
    /// 是否验证网络许可证
    /// </summary>
    public bool ValidateNetworkLicense { get; set; } = false;

    /// <summary>
    /// 验证超时时间（秒）
    /// </summary>
    public int TimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// 并发验证数量
    /// </summary>
    public int ConcurrentValidations { get; set; } = 5;

    /// <summary>
    /// 是否跳过已验证的许可证
    /// </summary>
    public bool SkipAlreadyValidated { get; set; } = false;
}

/// <summary>
/// 验证规则
/// </summary>
public class ValidationRule
{
    /// <summary>
    /// 规则ID
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// 规则名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 规则类型
    /// </summary>
    public ValidationRuleType Type { get; set; }

    /// <summary>
    /// 规则表达式
    /// </summary>
    public string Expression { get; set; } = string.Empty;

    /// <summary>
    /// 错误消息
    /// </summary>
    public string ErrorMessage { get; set; } = string.Empty;

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 优先级
    /// </summary>
    public int Priority { get; set; } = 0;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 验证级别
/// </summary>
public enum ValidationLevel
{
    Success,
    Info,
    Warning,
    Error,
    Critical
}

/// <summary>
/// 验证规则类型
/// </summary>
public enum ValidationRuleType
{
    License,
    Customer,
    AuthorizationType,
    AppTemplate,
    HardwareFingerprint,
    System
}
