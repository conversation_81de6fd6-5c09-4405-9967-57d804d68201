using System.IO.Compression;
using System.Text;
using System.Text.Json;

namespace LicenseManager.Utils;

/// <summary>
/// 文件操作工具类
/// 提供文件读写、压缩、备份等功能的静态方法
/// </summary>
public static class FileHelper
{
    /// <summary>
    /// 确保目录存在，如果不存在则创建
    /// </summary>
    /// <param name="directoryPath">目录路径</param>
    public static void EnsureDirectoryExists(string directoryPath)
    {
        if (!Directory.Exists(directoryPath))
        {
            Directory.CreateDirectory(directoryPath);
        }
    }

    /// <summary>
    /// 安全地写入文件（先写入临时文件，然后重命名）
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <param name="content">文件内容</param>
    /// <returns>是否成功</returns>
    public static async Task<bool> SafeWriteFileAsync(string filePath, byte[] content)
    {
        try
        {
            var directory = Path.GetDirectoryName(filePath);
            if (!string.IsNullOrEmpty(directory))
            {
                EnsureDirectoryExists(directory);
            }

            var tempFilePath = filePath + ".tmp";
            
            await File.WriteAllBytesAsync(tempFilePath, content);
            
            if (File.Exists(filePath))
            {
                File.Delete(filePath);
            }
            
            File.Move(tempFilePath, filePath);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 安全地写入文本文件
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <param name="content">文本内容</param>
    /// <param name="encoding">编码格式</param>
    /// <returns>是否成功</returns>
    public static async Task<bool> SafeWriteTextFileAsync(string filePath, string content, Encoding? encoding = null)
    {
        encoding ??= Encoding.UTF8;
        var bytes = encoding.GetBytes(content);
        return await SafeWriteFileAsync(filePath, bytes);
    }

    /// <summary>
    /// 读取文件内容
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>文件内容</returns>
    public static async Task<byte[]?> ReadFileAsync(string filePath)
    {
        try
        {
            if (!File.Exists(filePath))
            {
                return null;
            }
            
            return await File.ReadAllBytesAsync(filePath);
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// 读取文本文件内容
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <param name="encoding">编码格式</param>
    /// <returns>文本内容</returns>
    public static async Task<string?> ReadTextFileAsync(string filePath, Encoding? encoding = null)
    {
        try
        {
            if (!File.Exists(filePath))
            {
                return null;
            }
            
            encoding ??= Encoding.UTF8;
            return await File.ReadAllTextAsync(filePath, encoding);
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// 复制文件
    /// </summary>
    /// <param name="sourceFilePath">源文件路径</param>
    /// <param name="destinationFilePath">目标文件路径</param>
    /// <param name="overwrite">是否覆盖</param>
    /// <returns>是否成功</returns>
    public static bool CopyFile(string sourceFilePath, string destinationFilePath, bool overwrite = false)
    {
        try
        {
            var destinationDirectory = Path.GetDirectoryName(destinationFilePath);
            if (!string.IsNullOrEmpty(destinationDirectory))
            {
                EnsureDirectoryExists(destinationDirectory);
            }
            
            File.Copy(sourceFilePath, destinationFilePath, overwrite);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 移动文件
    /// </summary>
    /// <param name="sourceFilePath">源文件路径</param>
    /// <param name="destinationFilePath">目标文件路径</param>
    /// <param name="overwrite">是否覆盖</param>
    /// <returns>是否成功</returns>
    public static bool MoveFile(string sourceFilePath, string destinationFilePath, bool overwrite = false)
    {
        try
        {
            var destinationDirectory = Path.GetDirectoryName(destinationFilePath);
            if (!string.IsNullOrEmpty(destinationDirectory))
            {
                EnsureDirectoryExists(destinationDirectory);
            }
            
            if (overwrite && File.Exists(destinationFilePath))
            {
                File.Delete(destinationFilePath);
            }
            
            File.Move(sourceFilePath, destinationFilePath);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 删除文件
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>是否成功</returns>
    public static bool DeleteFile(string filePath)
    {
        try
        {
            if (File.Exists(filePath))
            {
                File.Delete(filePath);
            }
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 获取文件信息
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>文件信息</returns>
    public static FileInfoResult? GetFileInfo(string filePath)
    {
        try
        {
            if (!File.Exists(filePath))
            {
                return null;
            }
            
            var fileInfo = new FileInfo(filePath);
            return new FileInfoResult
            {
                FileName = fileInfo.Name,
                FilePath = fileInfo.FullName,
                FileSize = fileInfo.Length,
                CreatedTime = fileInfo.CreationTime,
                ModifiedTime = fileInfo.LastWriteTime,
                AccessedTime = fileInfo.LastAccessTime,
                Extension = fileInfo.Extension,
                IsReadOnly = fileInfo.IsReadOnly
            };
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// 压缩文件
    /// </summary>
    /// <param name="sourceFilePath">源文件路径</param>
    /// <param name="compressedFilePath">压缩文件路径</param>
    /// <returns>是否成功</returns>
    public static async Task<bool> CompressFileAsync(string sourceFilePath, string compressedFilePath)
    {
        try
        {
            var directory = Path.GetDirectoryName(compressedFilePath);
            if (!string.IsNullOrEmpty(directory))
            {
                EnsureDirectoryExists(directory);
            }
            
            using var originalFileStream = File.OpenRead(sourceFilePath);
            using var compressedFileStream = File.Create(compressedFilePath);
            using var compressionStream = new GZipStream(compressedFileStream, CompressionMode.Compress);
            
            await originalFileStream.CopyToAsync(compressionStream);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 解压缩文件
    /// </summary>
    /// <param name="compressedFilePath">压缩文件路径</param>
    /// <param name="decompressedFilePath">解压文件路径</param>
    /// <returns>是否成功</returns>
    public static async Task<bool> DecompressFileAsync(string compressedFilePath, string decompressedFilePath)
    {
        try
        {
            var directory = Path.GetDirectoryName(decompressedFilePath);
            if (!string.IsNullOrEmpty(directory))
            {
                EnsureDirectoryExists(directory);
            }
            
            using var compressedFileStream = File.OpenRead(compressedFilePath);
            using var decompressionStream = new GZipStream(compressedFileStream, CompressionMode.Decompress);
            using var decompressedFileStream = File.Create(decompressedFilePath);
            
            await decompressionStream.CopyToAsync(decompressedFileStream);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 备份文件
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <param name="backupDirectory">备份目录</param>
    /// <param name="includeTimestamp">是否包含时间戳</param>
    /// <returns>备份文件路径</returns>
    public static string? BackupFile(string filePath, string? backupDirectory = null, bool includeTimestamp = true)
    {
        try
        {
            if (!File.Exists(filePath))
            {
                return null;
            }
            
            var fileName = Path.GetFileNameWithoutExtension(filePath);
            var extension = Path.GetExtension(filePath);
            
            backupDirectory ??= Path.Combine(Path.GetDirectoryName(filePath) ?? "", "Backups");
            EnsureDirectoryExists(backupDirectory);
            
            var timestamp = includeTimestamp ? $"_{DateTime.Now:yyyyMMdd_HHmmss}" : "";
            var backupFileName = $"{fileName}{timestamp}{extension}";
            var backupFilePath = Path.Combine(backupDirectory, backupFileName);
            
            if (CopyFile(filePath, backupFilePath))
            {
                return backupFilePath;
            }
            
            return null;
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// 清理旧备份文件
    /// </summary>
    /// <param name="backupDirectory">备份目录</param>
    /// <param name="retentionDays">保留天数</param>
    /// <param name="filePattern">文件模式</param>
    /// <returns>删除的文件数量</returns>
    public static int CleanupOldBackups(string backupDirectory, int retentionDays = 30, string filePattern = "*")
    {
        try
        {
            if (!Directory.Exists(backupDirectory))
            {
                return 0;
            }
            
            var cutoffDate = DateTime.Now.AddDays(-retentionDays);
            var files = Directory.GetFiles(backupDirectory, filePattern);
            var deletedCount = 0;
            
            foreach (var file in files)
            {
                var fileInfo = new FileInfo(file);
                if (fileInfo.CreationTime < cutoffDate)
                {
                    if (DeleteFile(file))
                    {
                        deletedCount++;
                    }
                }
            }
            
            return deletedCount;
        }
        catch
        {
            return 0;
        }
    }

    /// <summary>
    /// 获取目录大小
    /// </summary>
    /// <param name="directoryPath">目录路径</param>
    /// <param name="includeSubdirectories">是否包含子目录</param>
    /// <returns>目录大小（字节）</returns>
    public static long GetDirectorySize(string directoryPath, bool includeSubdirectories = true)
    {
        try
        {
            if (!Directory.Exists(directoryPath))
            {
                return 0;
            }
            
            var directoryInfo = new DirectoryInfo(directoryPath);
            var searchOption = includeSubdirectories ? SearchOption.AllDirectories : SearchOption.TopDirectoryOnly;
            
            return directoryInfo.GetFiles("*", searchOption).Sum(file => file.Length);
        }
        catch
        {
            return 0;
        }
    }

    /// <summary>
    /// 保存对象为JSON文件
    /// </summary>
    /// <typeparam name="T">对象类型</typeparam>
    /// <param name="obj">要保存的对象</param>
    /// <param name="filePath">文件路径</param>
    /// <param name="options">JSON序列化选项</param>
    /// <returns>是否成功</returns>
    public static async Task<bool> SaveObjectAsJsonAsync<T>(T obj, string filePath, JsonSerializerOptions? options = null)
    {
        try
        {
            options ??= new JsonSerializerOptions { WriteIndented = true };
            var json = JsonSerializer.Serialize(obj, options);
            return await SafeWriteTextFileAsync(filePath, json);
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 从JSON文件加载对象
    /// </summary>
    /// <typeparam name="T">对象类型</typeparam>
    /// <param name="filePath">文件路径</param>
    /// <param name="options">JSON反序列化选项</param>
    /// <returns>加载的对象</returns>
    public static async Task<T?> LoadObjectFromJsonAsync<T>(string filePath, JsonSerializerOptions? options = null)
    {
        try
        {
            var json = await ReadTextFileAsync(filePath);
            if (string.IsNullOrEmpty(json))
            {
                return default(T);
            }
            
            return JsonSerializer.Deserialize<T>(json, options);
        }
        catch
        {
            return default(T);
        }
    }

    /// <summary>
    /// 生成唯一文件名
    /// </summary>
    /// <param name="directory">目录</param>
    /// <param name="fileName">文件名</param>
    /// <param name="extension">扩展名</param>
    /// <returns>唯一文件名</returns>
    public static string GenerateUniqueFileName(string directory, string fileName, string extension)
    {
        var baseName = Path.GetFileNameWithoutExtension(fileName);
        var counter = 1;
        var uniqueFileName = $"{baseName}{extension}";
        
        while (File.Exists(Path.Combine(directory, uniqueFileName)))
        {
            uniqueFileName = $"{baseName}_{counter}{extension}";
            counter++;
        }
        
        return uniqueFileName;
    }

    /// <summary>
    /// 获取临时文件路径
    /// </summary>
    /// <param name="extension">文件扩展名</param>
    /// <returns>临时文件路径</returns>
    public static string GetTempFilePath(string extension = ".tmp")
    {
        var tempDirectory = Path.GetTempPath();
        var fileName = $"{Guid.NewGuid()}{extension}";
        return Path.Combine(tempDirectory, fileName);
    }

    /// <summary>
    /// 验证文件路径
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>验证结果</returns>
    public static FilePathValidationResult ValidateFilePath(string filePath)
    {
        var result = new FilePathValidationResult();
        
        if (string.IsNullOrWhiteSpace(filePath))
        {
            result.IsValid = false;
            result.ErrorMessage = "文件路径不能为空";
            return result;
        }
        
        try
        {
            var fullPath = Path.GetFullPath(filePath);
            result.FullPath = fullPath;
            result.Directory = Path.GetDirectoryName(fullPath);
            result.FileName = Path.GetFileName(fullPath);
            result.Extension = Path.GetExtension(fullPath);
            result.IsValid = true;
        }
        catch (Exception ex)
        {
            result.IsValid = false;
            result.ErrorMessage = ex.Message;
        }
        
        return result;
    }

    /// <summary>
    /// 监控文件变化
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <param name="callback">变化回调</param>
    /// <returns>文件监控器</returns>
    public static IDisposable? WatchFile(string filePath, Action<string> callback)
    {
        try
        {
            var directory = Path.GetDirectoryName(filePath);
            var fileName = Path.GetFileName(filePath);

            if (string.IsNullOrEmpty(directory) || string.IsNullOrEmpty(fileName))
            {
                return null;
            }

            var watcher = new FileSystemWatcher(directory, fileName)
            {
                NotifyFilter = NotifyFilters.LastWrite | NotifyFilters.Size,
                EnableRaisingEvents = true
            };

            watcher.Changed += (sender, e) => callback(e.FullPath);

            return watcher;
        }
        catch
        {
            return null;
        }
    }
}

/// <summary>
/// 文件信息结果
/// </summary>
public class FileInfoResult
{
    /// <summary>
    /// 文件名
    /// </summary>
    public string FileName { get; set; } = string.Empty;

    /// <summary>
    /// 文件路径
    /// </summary>
    public string FilePath { get; set; } = string.Empty;

    /// <summary>
    /// 文件大小
    /// </summary>
    public long FileSize { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; }

    /// <summary>
    /// 修改时间
    /// </summary>
    public DateTime ModifiedTime { get; set; }

    /// <summary>
    /// 访问时间
    /// </summary>
    public DateTime AccessedTime { get; set; }

    /// <summary>
    /// 文件扩展名
    /// </summary>
    public string Extension { get; set; } = string.Empty;

    /// <summary>
    /// 是否只读
    /// </summary>
    public bool IsReadOnly { get; set; }
}

/// <summary>
/// 文件路径验证结果
/// </summary>
public class FilePathValidationResult
{
    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 完整路径
    /// </summary>
    public string? FullPath { get; set; }

    /// <summary>
    /// 目录
    /// </summary>
    public string? Directory { get; set; }

    /// <summary>
    /// 文件名
    /// </summary>
    public string? FileName { get; set; }

    /// <summary>
    /// 扩展名
    /// </summary>
    public string? Extension { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }
}
