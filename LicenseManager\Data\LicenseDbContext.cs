using Microsoft.EntityFrameworkCore;
using LicenseManager.Models;

namespace LicenseManager.Data;

/// <summary>
/// 许可证管理系统数据库上下文
/// </summary>
public class LicenseDbContext : DbContext
{
    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="options">数据库上下文选项</param>
    public LicenseDbContext(DbContextOptions<LicenseDbContext> options) : base(options)
    {
    }

    /// <summary>
    /// 许可证数据集
    /// </summary>
    public DbSet<License> Licenses { get; set; }

    /// <summary>
    /// 授权类型数据集
    /// </summary>
    public DbSet<AuthorizationType> AuthorizationTypes { get; set; }

    /// <summary>
    /// 应用程序模板数据集
    /// </summary>
    public DbSet<AppTemplate> AppTemplates { get; set; }

    /// <summary>
    /// 许可证信息数据集
    /// </summary>
    public DbSet<LicenseInfo> LicenseInfos { get; set; }

    /// <summary>
    /// 硬件指纹数据集
    /// </summary>
    public DbSet<HardwareFingerprint> HardwareFingerprints { get; set; }

    /// <summary>
    /// 配置数据模型
    /// </summary>
    /// <param name="modelBuilder">模型构建器</param>
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // 配置License实体
        ConfigureLicense(modelBuilder);

        // 配置AuthorizationType实体
        ConfigureAuthorizationType(modelBuilder);

        // 配置AppTemplate实体
        ConfigureAppTemplate(modelBuilder);

        // 配置LicenseInfo实体
        ConfigureLicenseInfo(modelBuilder);

        // 配置HardwareFingerprint实体
        ConfigureHardwareFingerprint(modelBuilder);

        // 添加种子数据
        SeedData(modelBuilder);
    }

    /// <summary>
    /// 配置License实体
    /// </summary>
    private static void ConfigureLicense(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<License>(entity =>
        {
            // 设置主键
            entity.HasKey(e => e.Id);

            // 配置索引
            entity.HasIndex(e => e.AppId).HasDatabaseName("IX_Licenses_AppId");
            entity.HasIndex(e => e.LicenseKey).IsUnique().HasDatabaseName("IX_Licenses_LicenseKey");
            entity.HasIndex(e => e.ExpiryDate).HasDatabaseName("IX_Licenses_ExpiryDate");
            entity.HasIndex(e => e.IsActive).HasDatabaseName("IX_Licenses_IsActive");
            entity.HasIndex(e => e.CustomerEmail).HasDatabaseName("IX_Licenses_CustomerEmail");

            // 配置外键关系
            entity.HasOne(e => e.AuthorizationType)
                  .WithMany(e => e.Licenses)
                  .HasForeignKey(e => e.AuthorizationTypeId)
                  .OnDelete(DeleteBehavior.Restrict);

            // 配置一对多关系
            entity.HasMany(e => e.LicenseInfos)
                  .WithOne(e => e.License)
                  .HasForeignKey(e => e.LicenseId)
                  .OnDelete(DeleteBehavior.Cascade);

            // 配置默认值
            entity.Property(e => e.IssueDate).HasDefaultValueSql("datetime('now')");
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("datetime('now')");
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("datetime('now')");
        });
    }

    /// <summary>
    /// 配置AuthorizationType实体
    /// </summary>
    private static void ConfigureAuthorizationType(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<AuthorizationType>(entity =>
        {
            // 设置主键
            entity.HasKey(e => e.Id);

            // 配置索引
            entity.HasIndex(e => e.Name).IsUnique().HasDatabaseName("IX_AuthorizationTypes_Name");
            entity.HasIndex(e => e.IsEnabled).HasDatabaseName("IX_AuthorizationTypes_IsEnabled");
            entity.HasIndex(e => e.IsTrial).HasDatabaseName("IX_AuthorizationTypes_IsTrial");
            entity.HasIndex(e => e.SortOrder).HasDatabaseName("IX_AuthorizationTypes_SortOrder");

            // 配置默认值
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("datetime('now')");
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("datetime('now')");
        });
    }

    /// <summary>
    /// 配置AppTemplate实体
    /// </summary>
    private static void ConfigureAppTemplate(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<AppTemplate>(entity =>
        {
            // 设置主键
            entity.HasKey(e => e.Id);

            // 配置索引
            entity.HasIndex(e => e.AppId).IsUnique().HasDatabaseName("IX_AppTemplates_AppId");
            entity.HasIndex(e => e.AppName).HasDatabaseName("IX_AppTemplates_AppName");
            entity.HasIndex(e => e.IsEnabled).HasDatabaseName("IX_AppTemplates_IsEnabled");
            entity.HasIndex(e => e.IsDefault).HasDatabaseName("IX_AppTemplates_IsDefault");

            // 配置外键关系
            entity.HasOne(e => e.DefaultAuthorizationType)
                  .WithMany()
                  .HasForeignKey(e => e.DefaultAuthorizationTypeId)
                  .OnDelete(DeleteBehavior.SetNull);

            // 配置默认值
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("datetime('now')");
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("datetime('now')");
        });
    }

    /// <summary>
    /// 配置LicenseInfo实体
    /// </summary>
    private static void ConfigureLicenseInfo(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<LicenseInfo>(entity =>
        {
            // 设置主键
            entity.HasKey(e => e.Id);

            // 配置复合索引
            entity.HasIndex(e => new { e.LicenseId, e.PropertyName })
                  .IsUnique()
                  .HasDatabaseName("IX_LicenseInfos_LicenseId_PropertyName");

            // 配置索引
            entity.HasIndex(e => e.PropertyGroup).HasDatabaseName("IX_LicenseInfos_PropertyGroup");
            entity.HasIndex(e => e.SortOrder).HasDatabaseName("IX_LicenseInfos_SortOrder");

            // 配置默认值
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("datetime('now')");
        });
    }

    /// <summary>
    /// 配置HardwareFingerprint实体
    /// </summary>
    private static void ConfigureHardwareFingerprint(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<HardwareFingerprint>(entity =>
        {
            // 设置主键
            entity.HasKey(e => e.Id);

            // 配置索引
            entity.HasIndex(e => e.FingerprintHash).IsUnique().HasDatabaseName("IX_HardwareFingerprints_Hash");
            entity.HasIndex(e => e.ComputerName).HasDatabaseName("IX_HardwareFingerprints_ComputerName");
            entity.HasIndex(e => e.IsEnabled).HasDatabaseName("IX_HardwareFingerprints_IsEnabled");
            entity.HasIndex(e => e.CreatedAt).HasDatabaseName("IX_HardwareFingerprints_CreatedAt");

            // 配置默认值
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("datetime('now')");
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("datetime('now')");
        });
    }

    /// <summary>
    /// 添加种子数据
    /// </summary>
    private static void SeedData(ModelBuilder modelBuilder)
    {
        // 添加默认授权类型
        modelBuilder.Entity<AuthorizationType>().HasData(
            new AuthorizationType
            {
                Id = 1,
                Name = "试用版",
                Description = "30天免费试用版本，功能受限",
                MaxUsers = 1,
                ValidityDays = 30,
                IsTrial = true,
                TrialDays = 30,
                Features = "[\"basic_features\"]",
                Priority = 1,
                SortOrder = 1,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new AuthorizationType
            {
                Id = 2,
                Name = "标准版",
                Description = "标准版本，包含基础功能",
                MaxUsers = 5,
                ValidityDays = 365,
                Features = "[\"basic_features\", \"standard_features\"]",
                Priority = 2,
                SortOrder = 2,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new AuthorizationType
            {
                Id = 3,
                Name = "专业版",
                Description = "专业版本，包含高级功能",
                MaxUsers = 20,
                ValidityDays = 365,
                Features = "[\"basic_features\", \"standard_features\", \"professional_features\"]",
                Priority = 3,
                SortOrder = 3,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new AuthorizationType
            {
                Id = 4,
                Name = "企业版",
                Description = "企业版本，包含所有功能",
                MaxUsers = -1,
                ValidityDays = 365,
                Features = "[\"basic_features\", \"standard_features\", \"professional_features\", \"enterprise_features\"]",
                Priority = 4,
                SortOrder = 4,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            }
        );

        // 添加默认应用程序模板
        modelBuilder.Entity<AppTemplate>().HasData(
            new AppTemplate
            {
                Id = 1,
                AppName = "示例应用程序",
                AppVersion = "1.0.0",
                AppId = "SampleApp",
                TemplateData = "{\"features\":[\"feature1\",\"feature2\"],\"settings\":{\"theme\":\"default\"}}",
                Description = "这是一个示例应用程序模板",
                Developer = "License Manager",
                DefaultAuthorizationTypeId = 2,
                IsDefault = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            }
        );
    }

    /// <summary>
    /// 保存更改时自动更新时间戳
    /// </summary>
    public override int SaveChanges()
    {
        UpdateTimestamps();
        return base.SaveChanges();
    }

    /// <summary>
    /// 异步保存更改时自动更新时间戳
    /// </summary>
    public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        UpdateTimestamps();
        return base.SaveChangesAsync(cancellationToken);
    }

    /// <summary>
    /// 更新实体的时间戳
    /// </summary>
    private void UpdateTimestamps()
    {
        var entries = ChangeTracker.Entries()
            .Where(e => e.State == EntityState.Added || e.State == EntityState.Modified);

        foreach (var entry in entries)
        {
            if (entry.Entity is License license)
            {
                if (entry.State == EntityState.Added)
                {
                    license.CreatedAt = DateTime.UtcNow;
                }
                license.UpdatedAt = DateTime.UtcNow;
            }
            else if (entry.Entity is AuthorizationType authType)
            {
                if (entry.State == EntityState.Added)
                {
                    authType.CreatedAt = DateTime.UtcNow;
                }
                authType.UpdatedAt = DateTime.UtcNow;
            }
            else if (entry.Entity is AppTemplate appTemplate)
            {
                if (entry.State == EntityState.Added)
                {
                    appTemplate.CreatedAt = DateTime.UtcNow;
                }
                appTemplate.UpdatedAt = DateTime.UtcNow;
            }
            else if (entry.Entity is HardwareFingerprint fingerprint)
            {
                if (entry.State == EntityState.Added)
                {
                    fingerprint.CreatedAt = DateTime.UtcNow;
                }
                fingerprint.UpdatedAt = DateTime.UtcNow;
            }
        }
    }
}
