{"Version": 1, "WorkspaceRootPath": "D:\\项目\\00 Crack\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{F9E9F745-333D-4E78-807F-D33A0FCCA46D}|LicenseManager\\LicenseManager.csproj|d:\\项目\\00 crack\\licensemanager\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{F9E9F745-333D-4E78-807F-D33A0FCCA46D}|LicenseManager\\LicenseManager.csproj|solutionrelative:licensemanager\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "MainWindow.xaml", "DocumentMoniker": "D:\\项目\\00 Crack\\LicenseManager\\MainWindow.xaml", "RelativeDocumentMoniker": "LicenseManager\\MainWindow.xaml", "ToolTip": "D:\\项目\\00 Crack\\LicenseManager\\MainWindow.xaml", "RelativeToolTip": "LicenseManager\\MainWindow.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-10T13:45:38.84Z", "EditorCaption": ""}]}]}]}